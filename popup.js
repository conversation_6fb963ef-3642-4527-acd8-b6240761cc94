// @Date    : 2020-09-12 16:26:48
// <AUTHOR> residuallaugh

// 在浏览器中打开使用手册
function openManual() {
    chrome.tabs.create({
        url: chrome.runtime.getURL('getstarted.html')
    });
}



// 通用的 appendChild 函数，自动检测并添加折叠功能
function appendChildWithCollapsible(parent, child) {
    parent.appendChild(child);
    
    // 如果添加的是 span 元素，检测是否需要折叠
    if (child.tagName === 'SPAN') {
        // 使用 setTimeout 延迟执行，避免阻塞UI
        setTimeout(() => {
            setupSpanCollapsible(child);
        }, 10);
    }
}

function setTextContentById(id){
    document.getElementById(id).textContent = chrome.i18n.getMessage(id);
}

function init_locales() {
    const popupIdList = [
        "Zhuye",
        "Peizhi",
        "popupIp",
        "popupIpPort",
        "popupDomain",
        "popupSfz",
        "popupMobile",
        "popupMail",
        "popupJwt",
        "popupAlgorithm",
        "popupSecret",
        "popupPath",
        "popupIncompletePath",
        "popupUrl",
        "popupStaticPath",
        "popupAnalyze",
    ];

    for (const id of popupIdList) {
        try{
            setTextContentById(id)
        }catch{
            console.log(id)
        }
    }

    
}

init_locales()

// 添加使用手册点击事件
document.addEventListener('DOMContentLoaded', function() {
    const manualLink = document.getElementById('manual-link');
    if (manualLink) {
        manualLink.addEventListener('click', function(e) {
            e.preventDefault();
            openManual();
        });
    }
});

var key = ["ip","ip_port","domain","path","incomplete_path","url","static","sfz","mobile","mail","jwt","algorithm","secret"]

function init_copy() {
    var elements = document.getElementsByClassName("copy");
    if(elements){
        for (var i=0, len=elements.length|0; i<len; i=i+1|0) {
            elements[i].textContent = chrome.i18n.getMessage("popupCopy");
            let ele_name = elements[i].name;
            let ele_id = elements[i].id;
            if (ele_id == "popupCopyurl"){
                elements[i].textContent = chrome.i18n.getMessage("popupCopyurl");
            }
            elements[i].onclick=function () {
                var copytext = document.getElementById(ele_name).textContent;
                if (ele_id == "popupCopyurl"){
                    Promise.all([getCurrentTab().then(function x(tab){
                        // console.log(tab);
                        if(tab == undefined){
                            alert(chrome.i18n.getMessage("popupTipClickBeforeCopy"))
                            return;
                        }
                        var url = new URL(tab.url)
                        var path_list = copytext.split('\n')
                        copytext = ""
                        for (var i = path_list.length - 1; i >= 0; i--) {
                            if(path_list[i][0] == '.'){
                                copytext += url.origin+'/'+path_list[i]+'\n';
                            }else{
                                copytext += url.origin+path_list[i]+'\n';
                            }
                        }
                        // 使用智能复制方案
                        smartCopy(copytext.slice(0, -1));
                    })]).then(res=> {})
                    // alert('复制成功');
                    return ;
                }
                // 使用智能复制方案
                smartCopy(copytext);
                // alert('复制成功');
            }
        }
    }
};


async function getCurrentTab() {
  let queryOptions = { active: true };
  let tabs = await chrome.tabs.query(queryOptions);
  let lastWindows = await chrome.windows.getLastFocused({})
  for( tab in tabs){
    // console.log(tab,lastWindows,tabs[tab].windowId === lastWindows.id)
    if (tabs[tab].windowId === lastWindows.id){
        return tabs[tab]
    }
  }
  return null;
}
function sleep (time) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

// 为span添加复制功能
function setupSpanCopy(span) {
    // 检查是否已经设置过复制功能，避免重复设置
    if (span.dataset.copySetup === 'true') {
        return;
    }
    
    // 添加右键菜单复制功能
    span.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // 异步处理复制，避免阻塞UI
        setTimeout(() => {
            try {
                const textToCopy = this.textContent.trim();
                if (textToCopy && textToCopy !== '🈚️') {
                    // 立即显示复制成功提示
                    showCopySuccess(this);
                    // 使用智能复制：先尝试现代API，失败则使用降级方案
                    smartCopy(textToCopy);
                }
            } catch (error) {
                console.warn('复制功能执行失败:', error);
            }
        }, 0);
    });
    
    // 标记已设置过复制功能
    span.dataset.copySetup = 'true';
}

// 检测span是否需要折叠并添加相应功能
function setupSpanCollapsible(span) {
    try {
        // 检查是否已经设置过折叠功能，避免重复设置
        if (span.dataset.collapsibleSetup === 'true') {
            return;
        }
        
        // 检查元素是否仍然在DOM中
        if (!document.contains(span)) {
            return;
        }
        
        // 检查文本内容是否为空或太短
        const textContent = span.textContent || '';
        if (!textContent.trim() || textContent.length < 50) {
            // 内容太短，不需要折叠，但仍然需要复制功能
            setupSpanCopy(span);
            span.dataset.collapsibleSetup = 'true';
            return;
        }
        
        // 高度检测：最准确的方式，检测实际显示高度
        const originalMaxHeight = span.style.maxHeight;
        const originalOverflow = span.style.overflow;
        
        // 临时移除高度限制来检测实际高度
        span.style.maxHeight = 'none';
        span.style.overflow = 'visible';
        
        // 获取实际高度
        const actualHeight = span.scrollHeight;
        
        // 恢复原始样式
        span.style.maxHeight = originalMaxHeight;
        span.style.overflow = originalOverflow;
        
        // 基于实际高度判断是否需要折叠（2.8em * 16px = 44.8px）
        const shouldCollapse = actualHeight > 44.8;
        
        if (shouldCollapse) {
            span.classList.add('collapsible', 'collapsed');
            
            // 添加点击事件处理折叠功能
            span.addEventListener('click', function(e) {
                // 阻止事件冒泡，避免触发链接点击
                e.stopPropagation();
                this.classList.toggle('expanded');
                this.classList.toggle('collapsed');
            });
        }
        
        // 为所有span添加右键菜单复制功能（无论是否折叠）
        setupSpanCopy(span);
        
        // 标记已设置过折叠功能
        span.dataset.collapsibleSetup = 'true';
    } catch (error) {
        console.warn('setupSpanCollapsible 失败:', error);
        // 即使失败也要添加复制功能
        try {
            setupSpanCopy(span);
        } catch (copyError) {
            console.warn('添加复制功能也失败:', copyError);
        }
        // 标记为已处理，避免重复尝试
        if (span && span.dataset) {
            span.dataset.collapsibleSetup = 'true';
        }
    }
}

// 重新检测所有span元素的折叠状态（备用函数，用于特殊情况下的全局刷新）
function refreshAllSpanCollapsible() {
    const allSpans = document.querySelectorAll('span');
    allSpans.forEach(span => {
        // 移除现有的事件监听器（通过克隆节点来清除）
        const newSpan = span.cloneNode(true);
        span.parentNode.replaceChild(newSpan, span);
        
        // 重新检测并设置折叠功能
        setupSpanCollapsible(newSpan);
    });
}

// 显示复制成功提示
function showCopySuccess(element) {
    try {
        // 移除已存在的提示
        const existingTooltips = document.querySelectorAll('.copy-success-tooltip');
        existingTooltips.forEach(tooltip => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        });
        
        // 创建提示元素
        const tooltip = document.createElement('div');
        tooltip.className = 'copy-success-tooltip';
        tooltip.textContent = '复制成功';
        tooltip.style.cssText = `
            position: fixed;
            background: #4CAF50;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        
        // 计算位置
        const rect = element.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2 - 40) + 'px';
        tooltip.style.top = (rect.top - 40) + 'px';
        
        // 添加到页面
        document.body.appendChild(tooltip);
        
        // 强制重绘后显示
        tooltip.offsetHeight; // 触发重绘
        tooltip.style.opacity = '1';
        
        // 自动移除
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 200);
        }, 1200);
    } catch (error) {
        console.warn('显示复制成功提示失败:', error);
    }
}



// 初始化区域折叠功能
function initSectionCollapse() {
    // 加载保存的折叠状态
    chrome.storage.local.get(['section_collapse_state'], function(result) {
        const collapseState = result.section_collapse_state || {};
        
        // 为每个标题添加点击事件
        const titles = document.querySelectorAll('.collapsible-title');
        titles.forEach(title => {
            const container = title.closest('.section-container');
            const sectionName = container.getAttribute('data-section');
            const content = container.querySelector('.section-content');
            
            // 设置初始状态
            if (collapseState[sectionName] === true) {
                title.classList.add('collapsed');
                content.classList.add('collapsed');
            } else {
                title.classList.add('expanded');
            }
            
            // 添加点击事件
            title.addEventListener('click', function(e) {
                // 阻止事件冒泡，避免触发其他事件
                e.stopPropagation();
                
                const isCollapsed = title.classList.contains('collapsed');
                
                if (isCollapsed) {
                    // 展开
                    title.classList.remove('collapsed');
                    title.classList.add('expanded');
                    content.classList.remove('collapsed');
                    collapseState[sectionName] = false;
                } else {
                    // 折叠
                    title.classList.remove('expanded');
                    title.classList.add('collapsed');
                    content.classList.add('collapsed');
                    collapseState[sectionName] = true;
                }
                
                // 保存状态
                chrome.storage.local.set({section_collapse_state: collapseState});
            });
        });
    });
}

// 现代复制方案（首选）
async function modernCopy(text) {
    try {
        await navigator.clipboard.writeText(text);
        console.log('使用现代API复制成功');
        return true;
    } catch (error) {
        console.warn('现代API复制失败，使用降级方案');
        return false;
    }
}

// 降级复制方案（备用）
function fallbackCopy(text) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.cssText = `
            position: fixed;
            left: -9999px;
            top: -9999px;
            width: 1px;
            height: 1px;
            opacity: 0;
            z-index: -1;
        `;
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const success = document.execCommand('copy');
        if (success) {
            console.log('使用降级方案复制成功');
        } else {
            console.error('降级复制失败');
        }
        
        // 立即移除元素
        document.body.removeChild(textArea);
    } catch (err) {
        console.error('降级复制出错:', err);
    }
}

// 智能复制函数：先尝试现代方案，失败则使用降级方案
async function smartCopy(text) {
    // 先尝试现代API
    const modernSuccess = await modernCopy(text);
    if (modernSuccess) {
        return true;
    }
    
    // 现代API失败，使用降级方案
    fallbackCopy(text);
    return false;
}

function show_info(result_data) {
    try {
        console.log("show_info", result_data)
        for (var k in key){
            if (result_data[key[k]]){
                try {
                    // console.log(result_data[key[k]])
                    let container = document.getElementById(key[k]);
                    if (!container) {
                        console.warn('容器不存在:', key[k]);
                        continue;
                    }
                    
                    while((ele = container.firstChild)){
                        ele.remove();
                    }
                    container.style.whiteSpace = "pre";
                    
                    for (var i in result_data[key[k]]){
                        try {
                            let tips = document.createElement("div");
                            tips.setAttribute("class", "tips")
                            let link = document.createElement("a");
                            let source = result_data['source'][result_data[key[k]][i]];
                            if (source) {
                                //虽然无法避免被xss，但插件默认提供了正确的CSP，这意味着我们即使不特殊处理，javascript也不会被执行。
                                // source = 'javascript:console.log`1`'
                                link.setAttribute("href", source);
                                link.setAttribute("title", source);
                            }
                            if ( result_data['ai'] &&  result_data['ai'][result_data[key[k]][i]]) {
                                tips.style.borderColor = '#000000'
                            }
                            link.appendChild(tips);
                            let span = document.createElement("span");
                            span.textContent = result_data[key[k]][i]+'\n';
                            
                            let br = document.createElement("br");
                            appendChildWithCollapsible(container, link);
                            appendChildWithCollapsible(container, span);
                            appendChildWithCollapsible(container, br);
                        } catch (error) {
                            console.warn('处理单个项目失败:', error);
                            continue;
                        }
                    }
                } catch (error) {
                    console.warn('处理容器失败:', key[k], error);
                    continue;
                }
            }
        }
        
        if(result_data['showCode'] && result_data['showCode'].length > 0){
            try {
                let popupshowCode = document.getElementById("showCode")
                if (popupshowCode) {
                    while((ele = popupshowCode.firstChild)){
                        ele.remove();
                    }
                    for(var i in result_data['showCode']){
                        showCode(result_data['showCode'][i], result_data['source'][result_data['showCode'][i]])
                    }
                }
            } catch (error) {
                console.warn('处理showCode失败:', error);
            }
        }
    } catch (error) {
        console.error('show_info 失败:', error);
    }
}


function showCode(code, source){
    try {
        // console.log("showCode")
        let popupshowCode = document.getElementById("showCode")
        if (!popupshowCode) {
            console.warn('showCode容器不存在');
            return;
        }
        
        popupshowCode.style.whiteSpace = "pre";
        let tips = document.createElement("div");
        tips.setAttribute("class", "tips")
        let link = document.createElement("a");
        link.setAttribute("href", source);
        link.setAttribute("title", source);
        link.appendChild(tips);
        appendChildWithCollapsible(popupshowCode, link);

        let tips2 = document.createElement("div");
        tips2.setAttribute("class", "tips2")
        let link2 = document.createElement("a");
        link2.setAttribute("href", "#");
        link2.setAttribute("title", chrome.i18n.getMessage("popupAIAnalysis"));
        link2.appendChild(tips2);
        link2.addEventListener('click', function(e) {
            e.preventDefault();
            let nextSpan = this.nextElementSibling;
            let prevA = this.previousElementSibling;
            let prevAsource = prevA.getAttribute("href")
            // console.log(prevA,prevA.getAttribute("href"))
            if (nextSpan && nextSpan.tagName === 'SPAN') {
                let content = nextSpan.textContent;
                getCurrentTab().then(function(tab) {
                    chrome.runtime.sendMessage({greeting: "AIcode", current: tab.url, code: content, from: prevAsource, tabid: tab.id});
                })
            }
        });

        appendChildWithCollapsible(popupshowCode, link2);

        let span = document.createElement("span");
        span.textContent = code+'\n';
        
        appendChildWithCollapsible(popupshowCode, span);
        let br = document.createElement("br");
        appendChildWithCollapsible(popupshowCode, br);
    } catch (error) {
        console.error('showCode 失败:', error);
    }
}




getCurrentTab().then(function get_info(tab) {
    chrome.storage.local.get(["findsomething_result_"+tab.url], function(result_data) {
        if (!result_data){
            return;
        }
        result_data = result_data["findsomething_result_"+tab.url]
        // console.log(result_data)
        if (!result_data){
            return;
        }
        show_info(result_data);
        if(result_data.donetasklist){
            if(result_data['done']!='done'){
               document.getElementById('taskstatus').textContent = chrome.i18n.getMessage("popupProcessing") + result_data['donetasklist'].length+"/"+result_data['tasklist'].length;
            }else{
                document.getElementById('taskstatus').textContent = chrome.i18n.getMessage("popupComplete") + result_data['donetasklist'].length+"/"+result_data['tasklist'].length;
            }
        }else{
            document.getElementById('taskstatus').textContent = chrome.i18n.getMessage("popupProcessing");
        }
        return;
    });
});

chrome.storage.onChanged.addListener(function(changes, namespace) {
    getCurrentTab().then(function get_info(tab) {
        for (let [key, {oldValue, newValue}] of Object.entries(changes)) {
            if(key=="findsomething_result_"+tab.url){
                const result_data = newValue;
                // console.log(newValue)
                show_info(result_data);
                if(result_data.donetasklist){
                    if(result_data['done']!='done'){
                       document.getElementById('taskstatus').textContent = chrome.i18n.getMessage("popupProcessing") + result_data['donetasklist'].length+"/"+result_data['tasklist'].length;
                    }else{
                        document.getElementById('taskstatus').textContent = chrome.i18n.getMessage("popupComplete") + result_data['donetasklist'].length+"/"+result_data['tasklist'].length;
                    }
                }else{
                    document.getElementById('taskstatus').textContent = chrome.i18n.getMessage("popupProcessing");
                }
            }
        }
    })
})

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.greeting === "showCode") {
        const message = request.message;
        const source = request.source;
        // console.log("Message received from background:", message);
        showCode(message, source)
    }
});

init_copy();
initSectionCollapse();

// ========== 艺爪会员集成 ===========
const vipButton = document.getElementById('vip-button');
const vipService = createEzrevenueService();

async function updateVipStatus() {
  // 检查VIP开关是否打开
  chrome.storage.local.get(["dynamicFunctionAnalysis"], function(dynamicAnalysisSettings){
    const isVipSwitchEnabled = dynamicAnalysisSettings["dynamicFunctionAnalysis"] === true;

    if (isVipSwitchEnabled) {
      // VIP开关打开，强制设置为VIP用户
      const isVip = true; // 强制设置为VIP
      vipButton.innerText = isVip ? chrome.i18n.getMessage("popupVipMember") : chrome.i18n.getMessage("popupVipSubscribe");
      console.log(!isVip)
      if(!isVip){
        document.getElementById('showCode').textContent = chrome.i18n.getMessage("popupVipFeature")
      }
    } else {
      // VIP开关关闭，直接显示"开通会员"
      vipButton.innerText = chrome.i18n.getMessage("popupVipSubscribe");
      document.getElementById('showCode').textContent = chrome.i18n.getMessage("popupVipFeature")
    }
  });
}

vipButton.addEventListener('click', async () => {
  // 检查VIP开关是否打开
  chrome.storage.local.get(["dynamicFunctionAnalysis"], function(dynamicAnalysisSettings){
    const isVipSwitchEnabled = dynamicAnalysisSettings["dynamicFunctionAnalysis"] === true;
    
    if (isVipSwitchEnabled) {
      // VIP开关打开，显示付费界面并更新状态
      vipService.showPaywallPopup().then(() => {
        updateVipStatus();
      });
    } else {
      // VIP开关关闭，直接显示付费界面
      vipService.showPaywallPopup();
    }
    chrome.runtime.sendMessage({greeting: 'refreshVipStatus'}, function() {
    });
  });
});

document.addEventListener('DOMContentLoaded', async () => {
  await updateVipStatus();
});

// 监听VIP开关状态变化
chrome.storage.onChanged.addListener(function(changes, namespace) {
  for (let [key, {oldValue, newValue}] of Object.entries(changes)) {
    if(key == "dynamicFunctionAnalysis"){
      updateVipStatus();
    }
  }
});
// ========== 艺爪会员集成 END ===========