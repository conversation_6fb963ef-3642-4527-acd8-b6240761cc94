/*! For license information please see background.bundle.js.LICENSE.txt */
(()=>{var e={268:function(e,t){!function(e){"use strict";function t(e){return"string"==typeof e?function(t){return t===e}:e||function(){return!0}}var r=function(e,t){this.node=e,this.state=t};function n(e,t){var r=Object.create(t||a);for(var n in e)r[n]=e[n];return r}function s(e,t,r){r(e,t)}function i(e,t,r){}var a={};a.Program=a.BlockStatement=a.StaticBlock=function(e,t,r){for(var n=0,s=e.body;n<s.length;n+=1)r(s[n],t,"Statement")},a.Statement=s,a.EmptyStatement=i,a.ExpressionStatement=a.ParenthesizedExpression=a.ChainExpression=function(e,t,r){return r(e.expression,t,"Expression")},a.IfStatement=function(e,t,r){r(e.test,t,"Expression"),r(e.consequent,t,"Statement"),e.alternate&&r(e.alternate,t,"Statement")},a.LabeledStatement=function(e,t,r){return r(e.body,t,"Statement")},a.BreakStatement=a.ContinueStatement=i,a.WithStatement=function(e,t,r){r(e.object,t,"Expression"),r(e.body,t,"Statement")},a.SwitchStatement=function(e,t,r){r(e.discriminant,t,"Expression");for(var n=0,s=e.cases;n<s.length;n+=1)r(s[n],t)},a.SwitchCase=function(e,t,r){e.test&&r(e.test,t,"Expression");for(var n=0,s=e.consequent;n<s.length;n+=1)r(s[n],t,"Statement")},a.ReturnStatement=a.YieldExpression=a.AwaitExpression=function(e,t,r){e.argument&&r(e.argument,t,"Expression")},a.ThrowStatement=a.SpreadElement=function(e,t,r){return r(e.argument,t,"Expression")},a.TryStatement=function(e,t,r){r(e.block,t,"Statement"),e.handler&&r(e.handler,t),e.finalizer&&r(e.finalizer,t,"Statement")},a.CatchClause=function(e,t,r){e.param&&r(e.param,t,"Pattern"),r(e.body,t,"Statement")},a.WhileStatement=a.DoWhileStatement=function(e,t,r){r(e.test,t,"Expression"),r(e.body,t,"Statement")},a.ForStatement=function(e,t,r){e.init&&r(e.init,t,"ForInit"),e.test&&r(e.test,t,"Expression"),e.update&&r(e.update,t,"Expression"),r(e.body,t,"Statement")},a.ForInStatement=a.ForOfStatement=function(e,t,r){r(e.left,t,"ForInit"),r(e.right,t,"Expression"),r(e.body,t,"Statement")},a.ForInit=function(e,t,r){"VariableDeclaration"===e.type?r(e,t):r(e,t,"Expression")},a.DebuggerStatement=i,a.FunctionDeclaration=function(e,t,r){return r(e,t,"Function")},a.VariableDeclaration=function(e,t,r){for(var n=0,s=e.declarations;n<s.length;n+=1)r(s[n],t)},a.VariableDeclarator=function(e,t,r){r(e.id,t,"Pattern"),e.init&&r(e.init,t,"Expression")},a.Function=function(e,t,r){e.id&&r(e.id,t,"Pattern");for(var n=0,s=e.params;n<s.length;n+=1)r(s[n],t,"Pattern");r(e.body,t,e.expression?"Expression":"Statement")},a.Pattern=function(e,t,r){"Identifier"===e.type?r(e,t,"VariablePattern"):"MemberExpression"===e.type?r(e,t,"MemberPattern"):r(e,t)},a.VariablePattern=i,a.MemberPattern=s,a.RestElement=function(e,t,r){return r(e.argument,t,"Pattern")},a.ArrayPattern=function(e,t,r){for(var n=0,s=e.elements;n<s.length;n+=1){var i=s[n];i&&r(i,t,"Pattern")}},a.ObjectPattern=function(e,t,r){for(var n=0,s=e.properties;n<s.length;n+=1){var i=s[n];"Property"===i.type?(i.computed&&r(i.key,t,"Expression"),r(i.value,t,"Pattern")):"RestElement"===i.type&&r(i.argument,t,"Pattern")}},a.Expression=s,a.ThisExpression=a.Super=a.MetaProperty=i,a.ArrayExpression=function(e,t,r){for(var n=0,s=e.elements;n<s.length;n+=1){var i=s[n];i&&r(i,t,"Expression")}},a.ObjectExpression=function(e,t,r){for(var n=0,s=e.properties;n<s.length;n+=1)r(s[n],t)},a.FunctionExpression=a.ArrowFunctionExpression=a.FunctionDeclaration,a.SequenceExpression=function(e,t,r){for(var n=0,s=e.expressions;n<s.length;n+=1)r(s[n],t,"Expression")},a.TemplateLiteral=function(e,t,r){for(var n=0,s=e.quasis;n<s.length;n+=1)r(s[n],t);for(var i=0,a=e.expressions;i<a.length;i+=1)r(a[i],t,"Expression")},a.TemplateElement=i,a.UnaryExpression=a.UpdateExpression=function(e,t,r){r(e.argument,t,"Expression")},a.BinaryExpression=a.LogicalExpression=function(e,t,r){r(e.left,t,"Expression"),r(e.right,t,"Expression")},a.AssignmentExpression=a.AssignmentPattern=function(e,t,r){r(e.left,t,"Pattern"),r(e.right,t,"Expression")},a.ConditionalExpression=function(e,t,r){r(e.test,t,"Expression"),r(e.consequent,t,"Expression"),r(e.alternate,t,"Expression")},a.NewExpression=a.CallExpression=function(e,t,r){if(r(e.callee,t,"Expression"),e.arguments)for(var n=0,s=e.arguments;n<s.length;n+=1)r(s[n],t,"Expression")},a.MemberExpression=function(e,t,r){r(e.object,t,"Expression"),e.computed&&r(e.property,t,"Expression")},a.ExportNamedDeclaration=a.ExportDefaultDeclaration=function(e,t,r){e.declaration&&r(e.declaration,t,"ExportNamedDeclaration"===e.type||e.declaration.id?"Statement":"Expression"),e.source&&r(e.source,t,"Expression")},a.ExportAllDeclaration=function(e,t,r){e.exported&&r(e.exported,t),r(e.source,t,"Expression")},a.ImportDeclaration=function(e,t,r){for(var n=0,s=e.specifiers;n<s.length;n+=1)r(s[n],t);r(e.source,t,"Expression")},a.ImportExpression=function(e,t,r){r(e.source,t,"Expression")},a.ImportSpecifier=a.ImportDefaultSpecifier=a.ImportNamespaceSpecifier=a.Identifier=a.PrivateIdentifier=a.Literal=i,a.TaggedTemplateExpression=function(e,t,r){r(e.tag,t,"Expression"),r(e.quasi,t,"Expression")},a.ClassDeclaration=a.ClassExpression=function(e,t,r){return r(e,t,"Class")},a.Class=function(e,t,r){e.id&&r(e.id,t,"Pattern"),e.superClass&&r(e.superClass,t,"Expression"),r(e.body,t)},a.ClassBody=function(e,t,r){for(var n=0,s=e.body;n<s.length;n+=1)r(s[n],t)},a.MethodDefinition=a.PropertyDefinition=a.Property=function(e,t,r){e.computed&&r(e.key,t,"Expression"),e.value&&r(e.value,t,"Expression")},e.ancestor=function(e,t,r,n,s){var i=[];r||(r=a),function e(n,s,a){var o=a||n.type,c=n!==i[i.length-1];c&&i.push(n),r[o](n,s,e),t[o]&&t[o](n,s||i,i),c&&i.pop()}(e,n,s)},e.base=a,e.findNodeAfter=function(e,n,s,i,o){s=t(s),i||(i=a);try{!function e(t,a,o){if(!(t.end<n)){var c=o||t.type;if(t.start>=n&&s(c,t))throw new r(t,a);i[c](t,a,e)}}(e,o)}catch(e){if(e instanceof r)return e;throw e}},e.findNodeAround=function(e,n,s,i,o){s=t(s),i||(i=a);try{!function e(t,a,o){var c=o||t.type;if(!(t.start>n||t.end<n)&&(i[c](t,a,e),s(c,t)))throw new r(t,a)}(e,o)}catch(e){if(e instanceof r)return e;throw e}},e.findNodeAt=function(e,n,s,i,o,c){o||(o=a),i=t(i);try{!function e(t,a,c){var l=c||t.type;if((null==n||t.start<=n)&&(null==s||t.end>=s)&&o[l](t,a,e),(null==n||t.start===n)&&(null==s||t.end===s)&&i(l,t))throw new r(t,a)}(e,c)}catch(e){if(e instanceof r)return e;throw e}},e.findNodeBefore=function(e,n,s,i,o){var c;return s=t(s),i||(i=a),function e(t,a,o){if(!(t.start>n)){var l=o||t.type;t.end<=n&&(!c||c.node.end<t.end)&&s(l,t)&&(c=new r(t,a)),i[l](t,a,e)}}(e,o),c},e.full=function(e,t,r,n,s){var i;r||(r=a),function e(n,s,a){var o=a||n.type;r[o](n,s,e),i!==n&&(t(n,s,o),i=n)}(e,n,s)},e.fullAncestor=function(e,t,r,n){r||(r=a);var s,i=[];!function e(n,a,o){var c=o||n.type,l=n!==i[i.length-1];l&&i.push(n),r[c](n,a,e),s!==n&&(t(n,a||i,i,c),s=n),l&&i.pop()}(e,n)},e.make=n,e.recursive=function(e,t,r,s,i){var a=r?n(r,s||void 0):s;!function e(t,r,n){a[n||t.type](t,r,e)}(e,t,i)},e.simple=function(e,t,r,n,s){r||(r=a),function e(n,s,i){var a=i||n.type;r[a](n,s,e),t[a]&&t[a](n,s)}(e,n,s)}}(t)},630:function(e,t){!function(e){"use strict";var t=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239],r=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],n="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࡰ-ࢇࢉ-ࢎࢠ-ࣉऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౝౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೝೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜑᜟ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭌᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲊᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꟍꟐꟑꟓꟕ-Ƛꟲ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",s={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},i="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",a={5:i,"5module":i+" export import",6:i+" const class extends export import super"},o=/^in(stanceof)?$/,c=new RegExp("["+n+"]"),l=new RegExp("["+n+"‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࢗ-࢟࣊-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄ఼ా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ೳഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-໎໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜕ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠏-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿ-ᫎᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷿‌‍‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯・꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿･]");function u(e,t){for(var r=65536,n=0;n<t.length;n+=2){if((r+=t[n])>e)return!1;if((r+=t[n+1])>=e)return!0}return!1}function h(e,t){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&c.test(String.fromCharCode(e)):!1!==t&&u(e,r)))}function p(e,n){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&l.test(String.fromCharCode(e)):!1!==n&&(u(e,r)||u(e,t)))))}var d=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null};function f(e,t){return new d(e,{beforeExpr:!0,binop:t})}var g={beforeExpr:!0},m={startsExpr:!0},w={};function y(e,t){return void 0===t&&(t={}),t.keyword=e,w[e]=new d(e,t)}var _={num:new d("num",m),regexp:new d("regexp",m),string:new d("string",m),name:new d("name",m),privateId:new d("privateId",m),eof:new d("eof"),bracketL:new d("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new d("]"),braceL:new d("{",{beforeExpr:!0,startsExpr:!0}),braceR:new d("}"),parenL:new d("(",{beforeExpr:!0,startsExpr:!0}),parenR:new d(")"),comma:new d(",",g),semi:new d(";",g),colon:new d(":",g),dot:new d("."),question:new d("?",g),questionDot:new d("?."),arrow:new d("=>",g),template:new d("template"),invalidTemplate:new d("invalidTemplate"),ellipsis:new d("...",g),backQuote:new d("`",m),dollarBraceL:new d("${",{beforeExpr:!0,startsExpr:!0}),eq:new d("=",{beforeExpr:!0,isAssign:!0}),assign:new d("_=",{beforeExpr:!0,isAssign:!0}),incDec:new d("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new d("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:f("||",1),logicalAND:f("&&",2),bitwiseOR:f("|",3),bitwiseXOR:f("^",4),bitwiseAND:f("&",5),equality:f("==/!=/===/!==",6),relational:f("</>/<=/>=",7),bitShift:f("<</>>/>>>",8),plusMin:new d("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:f("%",10),star:f("*",10),slash:f("/",10),starstar:new d("**",{beforeExpr:!0}),coalesce:f("??",1),_break:y("break"),_case:y("case",g),_catch:y("catch"),_continue:y("continue"),_debugger:y("debugger"),_default:y("default",g),_do:y("do",{isLoop:!0,beforeExpr:!0}),_else:y("else",g),_finally:y("finally"),_for:y("for",{isLoop:!0}),_function:y("function",m),_if:y("if"),_return:y("return",g),_switch:y("switch"),_throw:y("throw",g),_try:y("try"),_var:y("var"),_const:y("const"),_while:y("while",{isLoop:!0}),_with:y("with"),_new:y("new",{beforeExpr:!0,startsExpr:!0}),_this:y("this",m),_super:y("super",m),_class:y("class",m),_extends:y("extends",g),_export:y("export"),_import:y("import",m),_null:y("null",m),_true:y("true",m),_false:y("false",m),_in:y("in",{beforeExpr:!0,binop:7}),_instanceof:y("instanceof",{beforeExpr:!0,binop:7}),_typeof:y("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:y("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:y("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},S=/\r\n?|\n|\u2028|\u2029/,v=new RegExp(S.source,"g");function b(e){return 10===e||13===e||8232===e||8233===e}function x(e,t,r){void 0===r&&(r=e.length);for(var n=t;n<r;n++){var s=e.charCodeAt(n);if(b(s))return n<r-1&&13===s&&10===e.charCodeAt(n+1)?n+2:n+1}return-1}var k=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,A=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,E=Object.prototype,C=E.hasOwnProperty,P=E.toString,I=Object.hasOwn||function(e,t){return C.call(e,t)},T=Array.isArray||function(e){return"[object Array]"===P.call(e)},O=Object.create(null);function R(e){return O[e]||(O[e]=new RegExp("^(?:"+e.replace(/ /g,"|")+")$"))}function N(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}var j=/(?:[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/,L=function(e,t){this.line=e,this.column=t};L.prototype.offset=function(e){return new L(this.line,this.column+e)};var D=function(e,t,r){this.start=t,this.end=r,null!==e.sourceFile&&(this.source=e.sourceFile)};function V(e,t){for(var r=1,n=0;;){var s=x(e,n,t);if(s<0)return new L(r,t-n);++r,n=s}}var M={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:null,allowSuperOutsideMethod:null,allowHashBang:!1,checkPrivateFields:!0,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1},B=!1;function F(e){var t={};for(var r in M)t[r]=e&&I(e,r)?e[r]:M[r];if("latest"===t.ecmaVersion?t.ecmaVersion=1e8:null==t.ecmaVersion?(!B&&"object"==typeof console&&console.warn&&(B=!0,console.warn("Since Acorn 8.0.0, options.ecmaVersion is required.\nDefaulting to 2020, but this will stop working in the future.")),t.ecmaVersion=11):t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),e&&null!=e.allowHashBang||(t.allowHashBang=t.ecmaVersion>=14),T(t.onToken)){var n=t.onToken;t.onToken=function(e){return n.push(e)}}return T(t.onComment)&&(t.onComment=function(e,t){return function(r,n,s,i,a,o){var c={type:r?"Block":"Line",value:n,start:s,end:i};e.locations&&(c.loc=new D(this,a,o)),e.ranges&&(c.range=[s,i]),t.push(c)}}(t,t.onComment)),t}var $=256,U=259;function W(e,t){return 2|(e?4:0)|(t?8:0)}var q=function(e,t,r){this.options=e=F(e),this.sourceFile=e.sourceFile,this.keywords=R(a[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var n="";!0!==e.allowReserved&&(n=s[e.ecmaVersion>=6?6:5===e.ecmaVersion?5:3],"module"===e.sourceType&&(n+=" await")),this.reservedWords=R(n);var i=(n?n+" ":"")+s.strict;this.reservedWordsStrict=R(i),this.reservedWordsStrictBind=R(i+" "+s.strictBind),this.input=String(t),this.containsEsc=!1,r?(this.pos=r,this.lineStart=this.input.lastIndexOf("\n",r-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(S).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=_.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.potentialArrowInForAwait=!1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports=Object.create(null),0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null,this.privateNameStack=[]},H={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},canAwait:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0},allowNewDotTarget:{configurable:!0},inClassStaticBlock:{configurable:!0}};q.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)},H.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},H.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0},H.inAsync.get=function(){return(4&this.currentVarScope().flags)>0},H.canAwait.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e].flags;if(768&t)return!1;if(2&t)return(4&t)>0}return this.inModule&&this.options.ecmaVersion>=13||this.options.allowAwaitOutsideFunction},H.allowSuper.get=function(){return(64&this.currentThisScope().flags)>0||this.options.allowSuperOutsideMethod},H.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},H.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},H.allowNewDotTarget.get=function(){for(var e=this.scopeStack.length-1;e>=0;e--){var t=this.scopeStack[e].flags;if(768&t||2&t&&!(16&t))return!0}return!1},H.inClassStaticBlock.get=function(){return(this.currentVarScope().flags&$)>0},q.extend=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];for(var r=this,n=0;n<e.length;n++)r=e[n](r);return r},q.parse=function(e,t){return new this(t,e).parse()},q.parseExpressionAt=function(e,t,r){var n=new this(r,e,t);return n.nextToken(),n.parseExpression()},q.tokenizer=function(e,t){return new this(t,e)},Object.defineProperties(q.prototype,H);var K=q.prototype,z=/^(?:'((?:\\[^]|[^'\\])*?)'|"((?:\\[^]|[^"\\])*?)")/;K.strictDirective=function(e){if(this.options.ecmaVersion<5)return!1;for(;;){A.lastIndex=e,e+=A.exec(this.input)[0].length;var t=z.exec(this.input.slice(e));if(!t)return!1;if("use strict"===(t[1]||t[2])){A.lastIndex=e+t[0].length;var r=A.exec(this.input),n=r.index+r[0].length,s=this.input.charAt(n);return";"===s||"}"===s||S.test(r[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(s)||"!"===s&&"="===this.input.charAt(n+1))}e+=t[0].length,A.lastIndex=e,e+=A.exec(this.input)[0].length,";"===this.input[e]&&e++}},K.eat=function(e){return this.type===e&&(this.next(),!0)},K.isContextual=function(e){return this.type===_.name&&this.value===e&&!this.containsEsc},K.eatContextual=function(e){return!!this.isContextual(e)&&(this.next(),!0)},K.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},K.canInsertSemicolon=function(){return this.type===_.eof||this.type===_.braceR||S.test(this.input.slice(this.lastTokEnd,this.start))},K.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},K.semicolon=function(){this.eat(_.semi)||this.insertSemicolon()||this.unexpected()},K.afterTrailingComma=function(e,t){if(this.type===e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},K.expect=function(e){this.eat(e)||this.unexpected()},K.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")};var J=function(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1};K.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var r=t?e.parenthesizedAssign:e.parenthesizedBind;r>-1&&this.raiseRecoverable(r,t?"Assigning to rvalue":"Parenthesized pattern")}},K.checkExpressionErrors=function(e,t){if(!e)return!1;var r=e.shorthandAssign,n=e.doubleProto;if(!t)return r>=0||n>=0;r>=0&&this.raise(r,"Shorthand property assignments are valid only in destructuring patterns"),n>=0&&this.raiseRecoverable(n,"Redefinition of __proto__ property")},K.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},K.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var G=q.prototype;G.parseTopLevel=function(e){var t=Object.create(null);for(e.body||(e.body=[]);this.type!==_.eof;){var r=this.parseStatement(null,!0,t);e.body.push(r)}if(this.inModule)for(var n=0,s=Object.keys(this.undefinedExports);n<s.length;n+=1){var i=s[n];this.raiseRecoverable(this.undefinedExports[i].start,"Export '"+i+"' is not defined")}return this.adaptDirectivePrologue(e.body),this.next(),e.sourceType=this.options.sourceType,this.finishNode(e,"Program")};var X={kind:"loop"},Z={kind:"switch"};G.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;A.lastIndex=this.pos;var t=A.exec(this.input),r=this.pos+t[0].length,n=this.input.charCodeAt(r);if(91===n||92===n)return!0;if(e)return!1;if(123===n||n>55295&&n<56320)return!0;if(h(n,!0)){for(var s=r+1;p(n=this.input.charCodeAt(s),!0);)++s;if(92===n||n>55295&&n<56320)return!0;var i=this.input.slice(r,s);if(!o.test(i))return!0}return!1},G.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;A.lastIndex=this.pos;var e,t=A.exec(this.input),r=this.pos+t[0].length;return!(S.test(this.input.slice(this.pos,r))||"function"!==this.input.slice(r,r+8)||r+8!==this.input.length&&(p(e=this.input.charCodeAt(r+8))||e>55295&&e<56320))},G.isUsingKeyword=function(e,t){if(this.options.ecmaVersion<17||!this.isContextual(e?"await":"using"))return!1;A.lastIndex=this.pos;var r=A.exec(this.input),n=this.pos+r[0].length;if(S.test(this.input.slice(this.pos,n)))return!1;if(e){var s,i=n+5;if("using"!==this.input.slice(n,i)||i===this.input.length||p(s=this.input.charCodeAt(i))||s>55295&&s<56320)return!1;A.lastIndex=i;var a=A.exec(this.input);if(a&&S.test(this.input.slice(i,i+a[0].length)))return!1}if(t){var o,c=n+2;if(!("of"!==this.input.slice(n,c)||c!==this.input.length&&(p(o=this.input.charCodeAt(c))||o>55295&&o<56320)))return!1}var l=this.input.charCodeAt(n);return h(l,!0)||92===l},G.isAwaitUsing=function(e){return this.isUsingKeyword(!0,e)},G.isUsing=function(e){return this.isUsingKeyword(!1,e)},G.parseStatement=function(e,t,r){var n,s=this.type,i=this.startNode();switch(this.isLet(e)&&(s=_._var,n="let"),s){case _._break:case _._continue:return this.parseBreakContinueStatement(i,s.keyword);case _._debugger:return this.parseDebuggerStatement(i);case _._do:return this.parseDoStatement(i);case _._for:return this.parseForStatement(i);case _._function:return e&&(this.strict||"if"!==e&&"label"!==e)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(i,!1,!e);case _._class:return e&&this.unexpected(),this.parseClass(i,!0);case _._if:return this.parseIfStatement(i);case _._return:return this.parseReturnStatement(i);case _._switch:return this.parseSwitchStatement(i);case _._throw:return this.parseThrowStatement(i);case _._try:return this.parseTryStatement(i);case _._const:case _._var:return n=n||this.value,e&&"var"!==n&&this.unexpected(),this.parseVarStatement(i,n);case _._while:return this.parseWhileStatement(i);case _._with:return this.parseWithStatement(i);case _.braceL:return this.parseBlock(!0,i);case _.semi:return this.parseEmptyStatement(i);case _._export:case _._import:if(this.options.ecmaVersion>10&&s===_._import){A.lastIndex=this.pos;var a=A.exec(this.input),o=this.pos+a[0].length,c=this.input.charCodeAt(o);if(40===c||46===c)return this.parseExpressionStatement(i,this.parseExpression())}return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),s===_._import?this.parseImport(i):this.parseExport(i,r);default:if(this.isAsyncFunction())return e&&this.unexpected(),this.next(),this.parseFunctionStatement(i,!0,!e);var l=this.isAwaitUsing(!1)?"await using":this.isUsing(!1)?"using":null;if(l)return t&&"script"===this.options.sourceType&&this.raise(this.start,"Using declaration cannot appear in the top level when source type is `script`"),"await using"===l&&(this.canAwait||this.raise(this.start,"Await using cannot appear outside of async function"),this.next()),this.next(),this.parseVar(i,!1,l),this.semicolon(),this.finishNode(i,"VariableDeclaration");var u=this.value,h=this.parseExpression();return s===_.name&&"Identifier"===h.type&&this.eat(_.colon)?this.parseLabeledStatement(i,u,h,e):this.parseExpressionStatement(i,h)}},G.parseBreakContinueStatement=function(e,t){var r="break"===t;this.next(),this.eat(_.semi)||this.insertSemicolon()?e.label=null:this.type!==_.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var n=0;n<this.labels.length;++n){var s=this.labels[n];if(null==e.label||s.name===e.label.name){if(null!=s.kind&&(r||"loop"===s.kind))break;if(e.label&&r)break}}return n===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,r?"BreakStatement":"ContinueStatement")},G.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},G.parseDoStatement=function(e){return this.next(),this.labels.push(X),e.body=this.parseStatement("do"),this.labels.pop(),this.expect(_._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(_.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},G.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&this.canAwait&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(X),this.enterScope(0),this.expect(_.parenL),this.type===_.semi)return t>-1&&this.unexpected(t),this.parseFor(e,null);var r=this.isLet();if(this.type===_._var||this.type===_._const||r){var n=this.startNode(),s=r?"let":this.value;return this.next(),this.parseVar(n,!0,s),this.finishNode(n,"VariableDeclaration"),this.parseForAfterInit(e,n,t)}var i=this.isContextual("let"),a=!1,o=this.isUsing(!0)?"using":this.isAwaitUsing(!0)?"await using":null;if(o){var c=this.startNode();return this.next(),"await using"===o&&this.next(),this.parseVar(c,!0,o),this.finishNode(c,"VariableDeclaration"),this.parseForAfterInit(e,c,t)}var l=this.containsEsc,u=new J,h=this.start,p=t>-1?this.parseExprSubscripts(u,"await"):this.parseExpression(!0,u);return this.type===_._in||(a=this.options.ecmaVersion>=6&&this.isContextual("of"))?(t>-1?(this.type===_._in&&this.unexpected(t),e.await=!0):a&&this.options.ecmaVersion>=8&&(p.start!==h||l||"Identifier"!==p.type||"async"!==p.name?this.options.ecmaVersion>=9&&(e.await=!1):this.unexpected()),i&&a&&this.raise(p.start,"The left-hand side of a for-of loop may not start with 'let'."),this.toAssignable(p,!1,u),this.checkLValPattern(p),this.parseForIn(e,p)):(this.checkExpressionErrors(u,!0),t>-1&&this.unexpected(t),this.parseFor(e,p))},G.parseForAfterInit=function(e,t,r){return(this.type===_._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===t.declarations.length?(this.options.ecmaVersion>=9&&(this.type===_._in?r>-1&&this.unexpected(r):e.await=r>-1),this.parseForIn(e,t)):(r>-1&&this.unexpected(r),this.parseFor(e,t))},G.parseFunctionStatement=function(e,t,r){return this.next(),this.parseFunction(e,Y|(r?0:ee),!1,t)},G.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(_._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")},G.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(_.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},G.parseSwitchStatement=function(e){var t;this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(_.braceL),this.labels.push(Z),this.enterScope(0);for(var r=!1;this.type!==_.braceR;)if(this.type===_._case||this.type===_._default){var n=this.type===_._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),n?t.test=this.parseExpression():(r&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),r=!0,t.test=null),this.expect(_.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(null));return this.exitScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},G.parseThrowStatement=function(e){return this.next(),S.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var Q=[];G.parseCatchClauseParam=function(){var e=this.parseBindingAtom(),t="Identifier"===e.type;return this.enterScope(t?32:0),this.checkLValPattern(e,t?4:2),this.expect(_.parenR),e},G.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===_._catch){var t=this.startNode();this.next(),this.eat(_.parenL)?t.param=this.parseCatchClauseParam():(this.options.ecmaVersion<10&&this.unexpected(),t.param=null,this.enterScope(0)),t.body=this.parseBlock(!1),this.exitScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(_._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},G.parseVarStatement=function(e,t,r){return this.next(),this.parseVar(e,!1,t,r),this.semicolon(),this.finishNode(e,"VariableDeclaration")},G.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(X),e.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(e,"WhileStatement")},G.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement("with"),this.finishNode(e,"WithStatement")},G.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},G.parseLabeledStatement=function(e,t,r,n){for(var s=0,i=this.labels;s<i.length;s+=1)i[s].name===t&&this.raise(r.start,"Label '"+t+"' is already declared");for(var a=this.type.isLoop?"loop":this.type===_._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var c=this.labels[o];if(c.statementStart!==e.start)break;c.statementStart=this.start,c.kind=a}return this.labels.push({name:t,kind:a,statementStart:this.start}),e.body=this.parseStatement(n?-1===n.indexOf("label")?n+"label":n:"label"),this.labels.pop(),e.label=r,this.finishNode(e,"LabeledStatement")},G.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},G.parseBlock=function(e,t,r){for(void 0===e&&(e=!0),void 0===t&&(t=this.startNode()),t.body=[],this.expect(_.braceL),e&&this.enterScope(0);this.type!==_.braceR;){var n=this.parseStatement(null);t.body.push(n)}return r&&(this.strict=!1),this.next(),e&&this.exitScope(),this.finishNode(t,"BlockStatement")},G.parseFor=function(e,t){return e.init=t,this.expect(_.semi),e.test=this.type===_.semi?null:this.parseExpression(),this.expect(_.semi),e.update=this.type===_.parenR?null:this.parseExpression(),this.expect(_.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,"ForStatement")},G.parseForIn=function(e,t){var r=this.type===_._in;return this.next(),"VariableDeclaration"===t.type&&null!=t.declarations[0].init&&(!r||this.options.ecmaVersion<8||this.strict||"var"!==t.kind||"Identifier"!==t.declarations[0].id.type)&&this.raise(t.start,(r?"for-in":"for-of")+" loop variable declaration may not have an initializer"),e.left=t,e.right=r?this.parseExpression():this.parseMaybeAssign(),this.expect(_.parenR),e.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(e,r?"ForInStatement":"ForOfStatement")},G.parseVar=function(e,t,r,n){for(e.declarations=[],e.kind=r;;){var s=this.startNode();if(this.parseVarId(s,r),this.eat(_.eq)?s.init=this.parseMaybeAssign(t):n||"const"!==r||this.type===_._in||this.options.ecmaVersion>=6&&this.isContextual("of")?n||"using"!==r&&"await using"!==r||!(this.options.ecmaVersion>=17)||this.type===_._in||this.isContextual("of")?n||"Identifier"===s.id.type||t&&(this.type===_._in||this.isContextual("of"))?s.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.raise(this.lastTokEnd,"Missing initializer in "+r+" declaration"):this.unexpected(),e.declarations.push(this.finishNode(s,"VariableDeclarator")),!this.eat(_.comma))break}return e},G.parseVarId=function(e,t){e.id="using"===t||"await using"===t?this.parseIdent():this.parseBindingAtom(),this.checkLValPattern(e.id,"var"===t?1:2,!1)};var Y=1,ee=2;function te(e,t){var r=t.key.name,n=e[r],s="true";return"MethodDefinition"!==t.type||"get"!==t.kind&&"set"!==t.kind||(s=(t.static?"s":"i")+t.kind),"iget"===n&&"iset"===s||"iset"===n&&"iget"===s||"sget"===n&&"sset"===s||"sset"===n&&"sget"===s?(e[r]="true",!1):!!n||(e[r]=s,!1)}function re(e,t){var r=e.computed,n=e.key;return!r&&("Identifier"===n.type&&n.name===t||"Literal"===n.type&&n.value===t)}G.parseFunction=function(e,t,r,n,s){this.initFunction(e),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!n)&&(this.type===_.star&&t&ee&&this.unexpected(),e.generator=this.eat(_.star)),this.options.ecmaVersion>=8&&(e.async=!!n),t&Y&&(e.id=4&t&&this.type!==_.name?null:this.parseIdent(),!e.id||t&ee||this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?1:2:3));var i=this.yieldPos,a=this.awaitPos,o=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(W(e.async,e.generator)),t&Y||(e.id=this.type===_.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,r,!1,s),this.yieldPos=i,this.awaitPos=a,this.awaitIdentPos=o,this.finishNode(e,t&Y?"FunctionDeclaration":"FunctionExpression")},G.parseFunctionParams=function(e){this.expect(_.parenL),e.params=this.parseBindingList(_.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},G.parseClass=function(e,t){this.next();var r=this.strict;this.strict=!0,this.parseClassId(e,t),this.parseClassSuper(e);var n=this.enterClassBody(),s=this.startNode(),i=!1;for(s.body=[],this.expect(_.braceL);this.type!==_.braceR;){var a=this.parseClassElement(null!==e.superClass);a&&(s.body.push(a),"MethodDefinition"===a.type&&"constructor"===a.kind?(i&&this.raiseRecoverable(a.start,"Duplicate constructor in the same class"),i=!0):a.key&&"PrivateIdentifier"===a.key.type&&te(n,a)&&this.raiseRecoverable(a.key.start,"Identifier '#"+a.key.name+"' has already been declared"))}return this.strict=r,this.next(),e.body=this.finishNode(s,"ClassBody"),this.exitClassBody(),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},G.parseClassElement=function(e){if(this.eat(_.semi))return null;var t=this.options.ecmaVersion,r=this.startNode(),n="",s=!1,i=!1,a="method",o=!1;if(this.eatContextual("static")){if(t>=13&&this.eat(_.braceL))return this.parseClassStaticBlock(r),r;this.isClassElementNameStart()||this.type===_.star?o=!0:n="static"}if(r.static=o,!n&&t>=8&&this.eatContextual("async")&&(!this.isClassElementNameStart()&&this.type!==_.star||this.canInsertSemicolon()?n="async":i=!0),!n&&(t>=9||!i)&&this.eat(_.star)&&(s=!0),!n&&!i&&!s){var c=this.value;(this.eatContextual("get")||this.eatContextual("set"))&&(this.isClassElementNameStart()?a=c:n=c)}if(n?(r.computed=!1,r.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc),r.key.name=n,this.finishNode(r.key,"Identifier")):this.parseClassElementName(r),t<13||this.type===_.parenL||"method"!==a||s||i){var l=!r.static&&re(r,"constructor"),u=l&&e;l&&"method"!==a&&this.raise(r.key.start,"Constructor can't have get/set modifier"),r.kind=l?"constructor":a,this.parseClassMethod(r,s,i,u)}else this.parseClassField(r);return r},G.isClassElementNameStart=function(){return this.type===_.name||this.type===_.privateId||this.type===_.num||this.type===_.string||this.type===_.bracketL||this.type.keyword},G.parseClassElementName=function(e){this.type===_.privateId?("constructor"===this.value&&this.raise(this.start,"Classes can't have an element named '#constructor'"),e.computed=!1,e.key=this.parsePrivateIdent()):this.parsePropertyName(e)},G.parseClassMethod=function(e,t,r,n){var s=e.key;"constructor"===e.kind?(t&&this.raise(s.start,"Constructor can't be a generator"),r&&this.raise(s.start,"Constructor can't be an async method")):e.static&&re(e,"prototype")&&this.raise(s.start,"Classes may not have a static property named prototype");var i=e.value=this.parseMethod(t,r,n);return"get"===e.kind&&0!==i.params.length&&this.raiseRecoverable(i.start,"getter should have no params"),"set"===e.kind&&1!==i.params.length&&this.raiseRecoverable(i.start,"setter should have exactly one param"),"set"===e.kind&&"RestElement"===i.params[0].type&&this.raiseRecoverable(i.params[0].start,"Setter cannot use rest params"),this.finishNode(e,"MethodDefinition")},G.parseClassField=function(e){return re(e,"constructor")?this.raise(e.key.start,"Classes can't have a field named 'constructor'"):e.static&&re(e,"prototype")&&this.raise(e.key.start,"Classes can't have a static field named 'prototype'"),this.eat(_.eq)?(this.enterScope(576),e.value=this.parseMaybeAssign(),this.exitScope()):e.value=null,this.semicolon(),this.finishNode(e,"PropertyDefinition")},G.parseClassStaticBlock=function(e){e.body=[];var t=this.labels;for(this.labels=[],this.enterScope(320);this.type!==_.braceR;){var r=this.parseStatement(null);e.body.push(r)}return this.next(),this.exitScope(),this.labels=t,this.finishNode(e,"StaticBlock")},G.parseClassId=function(e,t){this.type===_.name?(e.id=this.parseIdent(),t&&this.checkLValSimple(e.id,2,!1)):(!0===t&&this.unexpected(),e.id=null)},G.parseClassSuper=function(e){e.superClass=this.eat(_._extends)?this.parseExprSubscripts(null,!1):null},G.enterClassBody=function(){var e={declared:Object.create(null),used:[]};return this.privateNameStack.push(e),e.declared},G.exitClassBody=function(){var e=this.privateNameStack.pop(),t=e.declared,r=e.used;if(this.options.checkPrivateFields)for(var n=this.privateNameStack.length,s=0===n?null:this.privateNameStack[n-1],i=0;i<r.length;++i){var a=r[i];I(t,a.name)||(s?s.used.push(a):this.raiseRecoverable(a.start,"Private field '#"+a.name+"' must be declared in an enclosing class"))}},G.parseExportAllDeclaration=function(e,t){return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(e.exported=this.parseModuleExportName(),this.checkExport(t,e.exported,this.lastTokStart)):e.exported=null),this.expectContextual("from"),this.type!==_.string&&this.unexpected(),e.source=this.parseExprAtom(),this.options.ecmaVersion>=16&&(e.attributes=this.parseWithClause()),this.semicolon(),this.finishNode(e,"ExportAllDeclaration")},G.parseExport=function(e,t){if(this.next(),this.eat(_.star))return this.parseExportAllDeclaration(e,t);if(this.eat(_._default))return this.checkExport(t,"default",this.lastTokStart),e.declaration=this.parseExportDefaultDeclaration(),this.finishNode(e,"ExportDefaultDeclaration");if(this.shouldParseExportStatement())e.declaration=this.parseExportDeclaration(e),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id,e.declaration.id.start),e.specifiers=[],e.source=null,this.options.ecmaVersion>=16&&(e.attributes=[]);else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))this.type!==_.string&&this.unexpected(),e.source=this.parseExprAtom(),this.options.ecmaVersion>=16&&(e.attributes=this.parseWithClause());else{for(var r=0,n=e.specifiers;r<n.length;r+=1){var s=n[r];this.checkUnreserved(s.local),this.checkLocalExport(s.local),"Literal"===s.local.type&&this.raise(s.local.start,"A string literal cannot be used as an exported binding without `from`.")}e.source=null,this.options.ecmaVersion>=16&&(e.attributes=[])}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},G.parseExportDeclaration=function(e){return this.parseStatement(null)},G.parseExportDefaultDeclaration=function(){var e;if(this.type===_._function||(e=this.isAsyncFunction())){var t=this.startNode();return this.next(),e&&this.next(),this.parseFunction(t,4|Y,!1,e)}if(this.type===_._class){var r=this.startNode();return this.parseClass(r,"nullableID")}var n=this.parseMaybeAssign();return this.semicolon(),n},G.checkExport=function(e,t,r){e&&("string"!=typeof t&&(t="Identifier"===t.type?t.name:t.value),I(e,t)&&this.raiseRecoverable(r,"Duplicate export '"+t+"'"),e[t]=!0)},G.checkPatternExport=function(e,t){var r=t.type;if("Identifier"===r)this.checkExport(e,t,t.start);else if("ObjectPattern"===r)for(var n=0,s=t.properties;n<s.length;n+=1){var i=s[n];this.checkPatternExport(e,i)}else if("ArrayPattern"===r)for(var a=0,o=t.elements;a<o.length;a+=1){var c=o[a];c&&this.checkPatternExport(e,c)}else"Property"===r?this.checkPatternExport(e,t.value):"AssignmentPattern"===r?this.checkPatternExport(e,t.left):"RestElement"===r&&this.checkPatternExport(e,t.argument)},G.checkVariableExport=function(e,t){if(e)for(var r=0,n=t;r<n.length;r+=1){var s=n[r];this.checkPatternExport(e,s.id)}},G.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},G.parseExportSpecifier=function(e){var t=this.startNode();return t.local=this.parseModuleExportName(),t.exported=this.eatContextual("as")?this.parseModuleExportName():t.local,this.checkExport(e,t.exported,t.exported.start),this.finishNode(t,"ExportSpecifier")},G.parseExportSpecifiers=function(e){var t=[],r=!0;for(this.expect(_.braceL);!this.eat(_.braceR);){if(r)r=!1;else if(this.expect(_.comma),this.afterTrailingComma(_.braceR))break;t.push(this.parseExportSpecifier(e))}return t},G.parseImport=function(e){return this.next(),this.type===_.string?(e.specifiers=Q,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===_.string?this.parseExprAtom():this.unexpected()),this.options.ecmaVersion>=16&&(e.attributes=this.parseWithClause()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},G.parseImportSpecifier=function(){var e=this.startNode();return e.imported=this.parseModuleExportName(),this.eatContextual("as")?e.local=this.parseIdent():(this.checkUnreserved(e.imported),e.local=e.imported),this.checkLValSimple(e.local,2),this.finishNode(e,"ImportSpecifier")},G.parseImportDefaultSpecifier=function(){var e=this.startNode();return e.local=this.parseIdent(),this.checkLValSimple(e.local,2),this.finishNode(e,"ImportDefaultSpecifier")},G.parseImportNamespaceSpecifier=function(){var e=this.startNode();return this.next(),this.expectContextual("as"),e.local=this.parseIdent(),this.checkLValSimple(e.local,2),this.finishNode(e,"ImportNamespaceSpecifier")},G.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===_.name&&(e.push(this.parseImportDefaultSpecifier()),!this.eat(_.comma)))return e;if(this.type===_.star)return e.push(this.parseImportNamespaceSpecifier()),e;for(this.expect(_.braceL);!this.eat(_.braceR);){if(t)t=!1;else if(this.expect(_.comma),this.afterTrailingComma(_.braceR))break;e.push(this.parseImportSpecifier())}return e},G.parseWithClause=function(){var e=[];if(!this.eat(_._with))return e;this.expect(_.braceL);for(var t={},r=!0;!this.eat(_.braceR);){if(r)r=!1;else if(this.expect(_.comma),this.afterTrailingComma(_.braceR))break;var n=this.parseImportAttribute(),s="Identifier"===n.key.type?n.key.name:n.key.value;I(t,s)&&this.raiseRecoverable(n.key.start,"Duplicate attribute key '"+s+"'"),t[s]=!0,e.push(n)}return e},G.parseImportAttribute=function(){var e=this.startNode();return e.key=this.type===_.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved),this.expect(_.colon),this.type!==_.string&&this.unexpected(),e.value=this.parseExprAtom(),this.finishNode(e,"ImportAttribute")},G.parseModuleExportName=function(){if(this.options.ecmaVersion>=13&&this.type===_.string){var e=this.parseLiteral(this.value);return j.test(e.value)&&this.raise(e.start,"An export name cannot include a lone surrogate."),e}return this.parseIdent(!0)},G.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},G.isDirectiveCandidate=function(e){return this.options.ecmaVersion>=5&&"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var ne=q.prototype;ne.toAssignable=function(e,t,r){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern",r&&this.checkPatternErrors(r,!0);for(var n=0,s=e.properties;n<s.length;n+=1){var i=s[n];this.toAssignable(i,t),"RestElement"!==i.type||"ArrayPattern"!==i.argument.type&&"ObjectPattern"!==i.argument.type||this.raise(i.argument.start,"Unexpected token")}break;case"Property":"init"!==e.kind&&this.raise(e.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern",r&&this.checkPatternErrors(r,!0),this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement",this.toAssignable(e.argument,t),"AssignmentPattern"===e.argument.type&&this.raise(e.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,"Only '=' operator can be used for specifying default value."),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,r);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}else r&&this.checkPatternErrors(r,!0);return e},ne.toAssignableList=function(e,t){for(var r=e.length,n=0;n<r;n++){var s=e[n];s&&this.toAssignable(s,t)}if(r){var i=e[r-1];6===this.options.ecmaVersion&&t&&i&&"RestElement"===i.type&&"Identifier"!==i.argument.type&&this.unexpected(i.argument.start)}return e},ne.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},ne.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==_.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},ne.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case _.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(_.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case _.braceL:return this.parseObj(!0)}return this.parseIdent()},ne.parseBindingList=function(e,t,r,n){for(var s=[],i=!0;!this.eat(e);)if(i?i=!1:this.expect(_.comma),t&&this.type===_.comma)s.push(null);else{if(r&&this.afterTrailingComma(e))break;if(this.type===_.ellipsis){var a=this.parseRestBinding();this.parseBindingListItem(a),s.push(a),this.type===_.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}s.push(this.parseAssignableListItem(n))}return s},ne.parseAssignableListItem=function(e){var t=this.parseMaybeDefault(this.start,this.startLoc);return this.parseBindingListItem(t),t},ne.parseBindingListItem=function(e){return e},ne.parseMaybeDefault=function(e,t,r){if(r=r||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(_.eq))return r;var n=this.startNodeAt(e,t);return n.left=r,n.right=this.parseMaybeAssign(),this.finishNode(n,"AssignmentPattern")},ne.checkLValSimple=function(e,t,r){void 0===t&&(t=0);var n=0!==t;switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(n?"Binding ":"Assigning to ")+e.name+" in strict mode"),n&&(2===t&&"let"===e.name&&this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name"),r&&(I(r,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),r[e.name]=!0),5!==t&&this.declareName(e.name,t,e.start));break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":n&&this.raiseRecoverable(e.start,"Binding member expression");break;case"ParenthesizedExpression":return n&&this.raiseRecoverable(e.start,"Binding parenthesized expression"),this.checkLValSimple(e.expression,t,r);default:this.raise(e.start,(n?"Binding":"Assigning to")+" rvalue")}},ne.checkLValPattern=function(e,t,r){switch(void 0===t&&(t=0),e.type){case"ObjectPattern":for(var n=0,s=e.properties;n<s.length;n+=1){var i=s[n];this.checkLValInnerPattern(i,t,r)}break;case"ArrayPattern":for(var a=0,o=e.elements;a<o.length;a+=1){var c=o[a];c&&this.checkLValInnerPattern(c,t,r)}break;default:this.checkLValSimple(e,t,r)}},ne.checkLValInnerPattern=function(e,t,r){switch(void 0===t&&(t=0),e.type){case"Property":this.checkLValInnerPattern(e.value,t,r);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,r);break;case"RestElement":this.checkLValPattern(e.argument,t,r);break;default:this.checkLValPattern(e,t,r)}};var se=function(e,t,r,n,s){this.token=e,this.isExpr=!!t,this.preserveSpace=!!r,this.override=n,this.generator=!!s},ie={b_stat:new se("{",!1),b_expr:new se("{",!0),b_tmpl:new se("${",!1),p_stat:new se("(",!1),p_expr:new se("(",!0),q_tmpl:new se("`",!0,!0,function(e){return e.tryReadTemplateToken()}),f_stat:new se("function",!1),f_expr:new se("function",!0),f_expr_gen:new se("function",!0,!1,null,!0),f_gen:new se("function",!1,!1,null,!0)},ae=q.prototype;ae.initialContext=function(){return[ie.b_stat]},ae.curContext=function(){return this.context[this.context.length-1]},ae.braceIsBlock=function(e){var t=this.curContext();return t===ie.f_expr||t===ie.f_stat||(e!==_.colon||t!==ie.b_stat&&t!==ie.b_expr?e===_._return||e===_.name&&this.exprAllowed?S.test(this.input.slice(this.lastTokEnd,this.start)):e===_._else||e===_.semi||e===_.eof||e===_.parenR||e===_.arrow||(e===_.braceL?t===ie.b_stat:e!==_._var&&e!==_._const&&e!==_.name&&!this.exprAllowed):!t.isExpr)},ae.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},ae.updateContext=function(e){var t,r=this.type;r.keyword&&e===_.dot?this.exprAllowed=!1:(t=r.updateContext)?t.call(this,e):this.exprAllowed=r.beforeExpr},ae.overrideContext=function(e){this.curContext()!==e&&(this.context[this.context.length-1]=e)},_.parenR.updateContext=_.braceR.updateContext=function(){if(1!==this.context.length){var e=this.context.pop();e===ie.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},_.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?ie.b_stat:ie.b_expr),this.exprAllowed=!0},_.dollarBraceL.updateContext=function(){this.context.push(ie.b_tmpl),this.exprAllowed=!0},_.parenL.updateContext=function(e){var t=e===_._if||e===_._for||e===_._with||e===_._while;this.context.push(t?ie.p_stat:ie.p_expr),this.exprAllowed=!0},_.incDec.updateContext=function(){},_._function.updateContext=_._class.updateContext=function(e){!e.beforeExpr||e===_._else||e===_.semi&&this.curContext()!==ie.p_stat||e===_._return&&S.test(this.input.slice(this.lastTokEnd,this.start))||(e===_.colon||e===_.braceL)&&this.curContext()===ie.b_stat?this.context.push(ie.f_stat):this.context.push(ie.f_expr),this.exprAllowed=!1},_.colon.updateContext=function(){"function"===this.curContext().token&&this.context.pop(),this.exprAllowed=!0},_.backQuote.updateContext=function(){this.curContext()===ie.q_tmpl?this.context.pop():this.context.push(ie.q_tmpl),this.exprAllowed=!1},_.star.updateContext=function(e){if(e===_._function){var t=this.context.length-1;this.context[t]===ie.f_expr?this.context[t]=ie.f_expr_gen:this.context[t]=ie.f_gen}this.exprAllowed=!0},_.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&e!==_.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var oe=q.prototype;function ce(e){return"Identifier"===e.type||"ParenthesizedExpression"===e.type&&ce(e.expression)}function le(e){return"MemberExpression"===e.type&&"PrivateIdentifier"===e.property.type||"ChainExpression"===e.type&&le(e.expression)||"ParenthesizedExpression"===e.type&&le(e.expression)}oe.checkPropClash=function(e,t,r){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===e.type||this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var n,s=e.key;switch(s.type){case"Identifier":n=s.name;break;case"Literal":n=String(s.value);break;default:return}var i=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===n&&"init"===i&&(t.proto&&(r?r.doubleProto<0&&(r.doubleProto=s.start):this.raiseRecoverable(s.start,"Redefinition of __proto__ property")),t.proto=!0);else{var a=t[n="$"+n];a?("init"===i?this.strict&&a.init||a.get||a.set:a.init||a[i])&&this.raiseRecoverable(s.start,"Redefinition of property"):a=t[n]={init:!1,get:!1,set:!1},a[i]=!0}}},oe.parseExpression=function(e,t){var r=this.start,n=this.startLoc,s=this.parseMaybeAssign(e,t);if(this.type===_.comma){var i=this.startNodeAt(r,n);for(i.expressions=[s];this.eat(_.comma);)i.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(i,"SequenceExpression")}return s},oe.parseMaybeAssign=function(e,t,r){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(e);this.exprAllowed=!1}var n=!1,s=-1,i=-1,a=-1;t?(s=t.parenthesizedAssign,i=t.trailingComma,a=t.doubleProto,t.parenthesizedAssign=t.trailingComma=-1):(t=new J,n=!0);var o=this.start,c=this.startLoc;this.type!==_.parenL&&this.type!==_.name||(this.potentialArrowAt=this.start,this.potentialArrowInForAwait="await"===e);var l=this.parseMaybeConditional(e,t);if(r&&(l=r.call(this,l,o,c)),this.type.isAssign){var u=this.startNodeAt(o,c);return u.operator=this.value,this.type===_.eq&&(l=this.toAssignable(l,!1,t)),n||(t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1),t.shorthandAssign>=l.start&&(t.shorthandAssign=-1),this.type===_.eq?this.checkLValPattern(l):this.checkLValSimple(l),u.left=l,this.next(),u.right=this.parseMaybeAssign(e),a>-1&&(t.doubleProto=a),this.finishNode(u,"AssignmentExpression")}return n&&this.checkExpressionErrors(t,!0),s>-1&&(t.parenthesizedAssign=s),i>-1&&(t.trailingComma=i),l},oe.parseMaybeConditional=function(e,t){var r=this.start,n=this.startLoc,s=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return s;if(this.eat(_.question)){var i=this.startNodeAt(r,n);return i.test=s,i.consequent=this.parseMaybeAssign(),this.expect(_.colon),i.alternate=this.parseMaybeAssign(e),this.finishNode(i,"ConditionalExpression")}return s},oe.parseExprOps=function(e,t){var r=this.start,n=this.startLoc,s=this.parseMaybeUnary(t,!1,!1,e);return this.checkExpressionErrors(t)||s.start===r&&"ArrowFunctionExpression"===s.type?s:this.parseExprOp(s,r,n,-1,e)},oe.parseExprOp=function(e,t,r,n,s){var i=this.type.binop;if(null!=i&&(!s||this.type!==_._in)&&i>n){var a=this.type===_.logicalOR||this.type===_.logicalAND,o=this.type===_.coalesce;o&&(i=_.logicalAND.binop);var c=this.value;this.next();var l=this.start,u=this.startLoc,h=this.parseExprOp(this.parseMaybeUnary(null,!1,!1,s),l,u,i,s),p=this.buildBinary(t,r,e,h,c,a||o);return(a&&this.type===_.coalesce||o&&(this.type===_.logicalOR||this.type===_.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(p,t,r,n,s)}return e},oe.buildBinary=function(e,t,r,n,s,i){"PrivateIdentifier"===n.type&&this.raise(n.start,"Private identifier can only be left side of binary expression");var a=this.startNodeAt(e,t);return a.left=r,a.operator=s,a.right=n,this.finishNode(a,i?"LogicalExpression":"BinaryExpression")},oe.parseMaybeUnary=function(e,t,r,n){var s,i=this.start,a=this.startLoc;if(this.isContextual("await")&&this.canAwait)s=this.parseAwait(n),t=!0;else if(this.type.prefix){var o=this.startNode(),c=this.type===_.incDec;o.operator=this.value,o.prefix=!0,this.next(),o.argument=this.parseMaybeUnary(null,!0,c,n),this.checkExpressionErrors(e,!0),c?this.checkLValSimple(o.argument):this.strict&&"delete"===o.operator&&ce(o.argument)?this.raiseRecoverable(o.start,"Deleting local variable in strict mode"):"delete"===o.operator&&le(o.argument)?this.raiseRecoverable(o.start,"Private fields can not be deleted"):t=!0,s=this.finishNode(o,c?"UpdateExpression":"UnaryExpression")}else if(t||this.type!==_.privateId){if(s=this.parseExprSubscripts(e,n),this.checkExpressionErrors(e))return s;for(;this.type.postfix&&!this.canInsertSemicolon();){var l=this.startNodeAt(i,a);l.operator=this.value,l.prefix=!1,l.argument=s,this.checkLValSimple(s),this.next(),s=this.finishNode(l,"UpdateExpression")}}else(n||0===this.privateNameStack.length)&&this.options.checkPrivateFields&&this.unexpected(),s=this.parsePrivateIdent(),this.type!==_._in&&this.unexpected();return r||!this.eat(_.starstar)?s:t?void this.unexpected(this.lastTokStart):this.buildBinary(i,a,s,this.parseMaybeUnary(null,!1,!1,n),"**",!1)},oe.parseExprSubscripts=function(e,t){var r=this.start,n=this.startLoc,s=this.parseExprAtom(e,t);if("ArrowFunctionExpression"===s.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return s;var i=this.parseSubscripts(s,r,n,!1,t);return e&&"MemberExpression"===i.type&&(e.parenthesizedAssign>=i.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=i.start&&(e.parenthesizedBind=-1),e.trailingComma>=i.start&&(e.trailingComma=-1)),i},oe.parseSubscripts=function(e,t,r,n,s){for(var i=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.potentialArrowAt===e.start,a=!1;;){var o=this.parseSubscript(e,t,r,n,i,a,s);if(o.optional&&(a=!0),o===e||"ArrowFunctionExpression"===o.type){if(a){var c=this.startNodeAt(t,r);c.expression=o,o=this.finishNode(c,"ChainExpression")}return o}e=o}},oe.shouldParseAsyncArrow=function(){return!this.canInsertSemicolon()&&this.eat(_.arrow)},oe.parseSubscriptAsyncArrow=function(e,t,r,n){return this.parseArrowExpression(this.startNodeAt(e,t),r,!0,n)},oe.parseSubscript=function(e,t,r,n,s,i,a){var o=this.options.ecmaVersion>=11,c=o&&this.eat(_.questionDot);n&&c&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var l=this.eat(_.bracketL);if(l||c&&this.type!==_.parenL&&this.type!==_.backQuote||this.eat(_.dot)){var u=this.startNodeAt(t,r);u.object=e,l?(u.property=this.parseExpression(),this.expect(_.bracketR)):this.type===_.privateId&&"Super"!==e.type?u.property=this.parsePrivateIdent():u.property=this.parseIdent("never"!==this.options.allowReserved),u.computed=!!l,o&&(u.optional=c),e=this.finishNode(u,"MemberExpression")}else if(!n&&this.eat(_.parenL)){var h=new J,p=this.yieldPos,d=this.awaitPos,f=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var g=this.parseExprList(_.parenR,this.options.ecmaVersion>=8,!1,h);if(s&&!c&&this.shouldParseAsyncArrow())return this.checkPatternErrors(h,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=p,this.awaitPos=d,this.awaitIdentPos=f,this.parseSubscriptAsyncArrow(t,r,g,a);this.checkExpressionErrors(h,!0),this.yieldPos=p||this.yieldPos,this.awaitPos=d||this.awaitPos,this.awaitIdentPos=f||this.awaitIdentPos;var m=this.startNodeAt(t,r);m.callee=e,m.arguments=g,o&&(m.optional=c),e=this.finishNode(m,"CallExpression")}else if(this.type===_.backQuote){(c||i)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var w=this.startNodeAt(t,r);w.tag=e,w.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(w,"TaggedTemplateExpression")}return e},oe.parseExprAtom=function(e,t,r){this.type===_.slash&&this.readRegexp();var n,s=this.potentialArrowAt===this.start;switch(this.type){case _._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),n=this.startNode(),this.next(),this.type!==_.parenL||this.allowDirectSuper||this.raise(n.start,"super() call outside constructor of a subclass"),this.type!==_.dot&&this.type!==_.bracketL&&this.type!==_.parenL&&this.unexpected(),this.finishNode(n,"Super");case _._this:return n=this.startNode(),this.next(),this.finishNode(n,"ThisExpression");case _.name:var i=this.start,a=this.startLoc,o=this.containsEsc,c=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!o&&"async"===c.name&&!this.canInsertSemicolon()&&this.eat(_._function))return this.overrideContext(ie.f_expr),this.parseFunction(this.startNodeAt(i,a),0,!1,!0,t);if(s&&!this.canInsertSemicolon()){if(this.eat(_.arrow))return this.parseArrowExpression(this.startNodeAt(i,a),[c],!1,t);if(this.options.ecmaVersion>=8&&"async"===c.name&&this.type===_.name&&!o&&(!this.potentialArrowInForAwait||"of"!==this.value||this.containsEsc))return c=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(_.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(i,a),[c],!0,t)}return c;case _.regexp:var l=this.value;return(n=this.parseLiteral(l.value)).regex={pattern:l.pattern,flags:l.flags},n;case _.num:case _.string:return this.parseLiteral(this.value);case _._null:case _._true:case _._false:return(n=this.startNode()).value=this.type===_._null?null:this.type===_._true,n.raw=this.type.keyword,this.next(),this.finishNode(n,"Literal");case _.parenL:var u=this.start,h=this.parseParenAndDistinguishExpression(s,t);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(h)&&(e.parenthesizedAssign=u),e.parenthesizedBind<0&&(e.parenthesizedBind=u)),h;case _.bracketL:return n=this.startNode(),this.next(),n.elements=this.parseExprList(_.bracketR,!0,!0,e),this.finishNode(n,"ArrayExpression");case _.braceL:return this.overrideContext(ie.b_expr),this.parseObj(!1,e);case _._function:return n=this.startNode(),this.next(),this.parseFunction(n,0);case _._class:return this.parseClass(this.startNode(),!1);case _._new:return this.parseNew();case _.backQuote:return this.parseTemplate();case _._import:return this.options.ecmaVersion>=11?this.parseExprImport(r):this.unexpected();default:return this.parseExprAtomDefault()}},oe.parseExprAtomDefault=function(){this.unexpected()},oe.parseExprImport=function(e){var t=this.startNode();if(this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import"),this.next(),this.type===_.parenL&&!e)return this.parseDynamicImport(t);if(this.type===_.dot){var r=this.startNodeAt(t.start,t.loc&&t.loc.start);return r.name="import",t.meta=this.finishNode(r,"Identifier"),this.parseImportMeta(t)}this.unexpected()},oe.parseDynamicImport=function(e){if(this.next(),e.source=this.parseMaybeAssign(),this.options.ecmaVersion>=16)this.eat(_.parenR)?e.options=null:(this.expect(_.comma),this.afterTrailingComma(_.parenR)?e.options=null:(e.options=this.parseMaybeAssign(),this.eat(_.parenR)||(this.expect(_.comma),this.afterTrailingComma(_.parenR)||this.unexpected())));else if(!this.eat(_.parenR)){var t=this.start;this.eat(_.comma)&&this.eat(_.parenR)?this.raiseRecoverable(t,"Trailing comma is not allowed in import()"):this.unexpected(t)}return this.finishNode(e,"ImportExpression")},oe.parseImportMeta=function(e){this.next();var t=this.containsEsc;return e.property=this.parseIdent(!0),"meta"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'"),t&&this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters"),"module"===this.options.sourceType||this.options.allowImportExportEverywhere||this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module"),this.finishNode(e,"MetaProperty")},oe.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),110===t.raw.charCodeAt(t.raw.length-1)&&(t.bigint=null!=t.value?t.value.toString():t.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(t,"Literal")},oe.parseParenExpression=function(){this.expect(_.parenL);var e=this.parseExpression();return this.expect(_.parenR),e},oe.shouldParseArrow=function(e){return!this.canInsertSemicolon()},oe.parseParenAndDistinguishExpression=function(e,t){var r,n=this.start,s=this.startLoc,i=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var a,o=this.start,c=this.startLoc,l=[],u=!0,h=!1,p=new J,d=this.yieldPos,f=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==_.parenR;){if(u?u=!1:this.expect(_.comma),i&&this.afterTrailingComma(_.parenR,!0)){h=!0;break}if(this.type===_.ellipsis){a=this.start,l.push(this.parseParenItem(this.parseRestBinding())),this.type===_.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element");break}l.push(this.parseMaybeAssign(!1,p,this.parseParenItem))}var g=this.lastTokEnd,m=this.lastTokEndLoc;if(this.expect(_.parenR),e&&this.shouldParseArrow(l)&&this.eat(_.arrow))return this.checkPatternErrors(p,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=d,this.awaitPos=f,this.parseParenArrowList(n,s,l,t);l.length&&!h||this.unexpected(this.lastTokStart),a&&this.unexpected(a),this.checkExpressionErrors(p,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=f||this.awaitPos,l.length>1?((r=this.startNodeAt(o,c)).expressions=l,this.finishNodeAt(r,"SequenceExpression",g,m)):r=l[0]}else r=this.parseParenExpression();if(this.options.preserveParens){var w=this.startNodeAt(n,s);return w.expression=r,this.finishNode(w,"ParenthesizedExpression")}return r},oe.parseParenItem=function(e){return e},oe.parseParenArrowList=function(e,t,r,n){return this.parseArrowExpression(this.startNodeAt(e,t),r,!1,n)};var ue=[];oe.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var e=this.startNode();if(this.next(),this.options.ecmaVersion>=6&&this.type===_.dot){var t=this.startNodeAt(e.start,e.loc&&e.loc.start);t.name="new",e.meta=this.finishNode(t,"Identifier"),this.next();var r=this.containsEsc;return e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'"),r&&this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters"),this.allowNewDotTarget||this.raiseRecoverable(e.start,"'new.target' can only be used in functions and class static block"),this.finishNode(e,"MetaProperty")}var n=this.start,s=this.startLoc;return e.callee=this.parseSubscripts(this.parseExprAtom(null,!1,!0),n,s,!0,!1),this.eat(_.parenL)?e.arguments=this.parseExprList(_.parenR,this.options.ecmaVersion>=8,!1):e.arguments=ue,this.finishNode(e,"NewExpression")},oe.parseTemplateElement=function(e){var t=e.isTagged,r=this.startNode();return this.type===_.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),r.value={raw:this.value.replace(/\r\n?/g,"\n"),cooked:null}):r.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),r.tail=this.type===_.backQuote,this.finishNode(r,"TemplateElement")},oe.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var r=this.startNode();this.next(),r.expressions=[];var n=this.parseTemplateElement({isTagged:t});for(r.quasis=[n];!n.tail;)this.type===_.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(_.dollarBraceL),r.expressions.push(this.parseExpression()),this.expect(_.braceR),r.quasis.push(n=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(r,"TemplateLiteral")},oe.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===_.name||this.type===_.num||this.type===_.string||this.type===_.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===_.star)&&!S.test(this.input.slice(this.lastTokEnd,this.start))},oe.parseObj=function(e,t){var r=this.startNode(),n=!0,s={};for(r.properties=[],this.next();!this.eat(_.braceR);){if(n)n=!1;else if(this.expect(_.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(_.braceR))break;var i=this.parseProperty(e,t);e||this.checkPropClash(i,s,t),r.properties.push(i)}return this.finishNode(r,e?"ObjectPattern":"ObjectExpression")},oe.parseProperty=function(e,t){var r,n,s,i,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(_.ellipsis))return e?(a.argument=this.parseIdent(!1),this.type===_.comma&&this.raiseRecoverable(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(a.argument=this.parseMaybeAssign(!1,t),this.type===_.comma&&t&&t.trailingComma<0&&(t.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(e||t)&&(s=this.start,i=this.startLoc),e||(r=this.eat(_.star)));var o=this.containsEsc;return this.parsePropertyName(a),!e&&!o&&this.options.ecmaVersion>=8&&!r&&this.isAsyncProp(a)?(n=!0,r=this.options.ecmaVersion>=9&&this.eat(_.star),this.parsePropertyName(a)):n=!1,this.parsePropertyValue(a,e,r,n,s,i,t,o),this.finishNode(a,"Property")},oe.parseGetterSetter=function(e){var t=e.key.name;this.parsePropertyName(e),e.value=this.parseMethod(!1),e.kind=t;var r="get"===e.kind?0:1;if(e.value.params.length!==r){var n=e.value.start;"get"===e.kind?this.raiseRecoverable(n,"getter should have no params"):this.raiseRecoverable(n,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")},oe.parsePropertyValue=function(e,t,r,n,s,i,a,o){(r||n)&&this.type===_.colon&&this.unexpected(),this.eat(_.colon)?(e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),e.kind="init"):this.options.ecmaVersion>=6&&this.type===_.parenL?(t&&this.unexpected(),e.method=!0,e.value=this.parseMethod(r,n),e.kind="init"):t||o||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type===_.comma||this.type===_.braceR||this.type===_.eq?this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?((r||n)&&this.unexpected(),this.checkUnreserved(e.key),"await"!==e.key.name||this.awaitIdentPos||(this.awaitIdentPos=s),t?e.value=this.parseMaybeDefault(s,i,this.copyNode(e.key)):this.type===_.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),e.value=this.parseMaybeDefault(s,i,this.copyNode(e.key))):e.value=this.copyNode(e.key),e.kind="init",e.shorthand=!0):this.unexpected():((r||n)&&this.unexpected(),this.parseGetterSetter(e))},oe.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(_.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(_.bracketR),e.key;e.computed=!1}return e.key=this.type===_.num||this.type===_.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},oe.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},oe.parseMethod=function(e,t,r){var n=this.startNode(),s=this.yieldPos,i=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(n),this.options.ecmaVersion>=6&&(n.generator=e),this.options.ecmaVersion>=8&&(n.async=!!t),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|W(t,n.generator)|(r?128:0)),this.expect(_.parenL),n.params=this.parseBindingList(_.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(n,!1,!0,!1),this.yieldPos=s,this.awaitPos=i,this.awaitIdentPos=a,this.finishNode(n,"FunctionExpression")},oe.parseArrowExpression=function(e,t,r,n){var s=this.yieldPos,i=this.awaitPos,a=this.awaitIdentPos;return this.enterScope(16|W(r,!1)),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!r),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0,!1,n),this.yieldPos=s,this.awaitPos=i,this.awaitIdentPos=a,this.finishNode(e,"ArrowFunctionExpression")},oe.parseFunctionBody=function(e,t,r,n){var s=t&&this.type!==_.braceL,i=this.strict,a=!1;if(s)e.body=this.parseMaybeAssign(n),e.expression=!0,this.checkParams(e,!1);else{var o=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);i&&!o||(a=this.strictDirective(this.end))&&o&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var c=this.labels;this.labels=[],a&&(this.strict=!0),this.checkParams(e,!i&&!a&&!t&&!r&&this.isSimpleParamList(e.params)),this.strict&&e.id&&this.checkLValSimple(e.id,5),e.body=this.parseBlock(!1,void 0,a&&!i),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=c}this.exitScope()},oe.isSimpleParamList=function(e){for(var t=0,r=e;t<r.length;t+=1)if("Identifier"!==r[t].type)return!1;return!0},oe.checkParams=function(e,t){for(var r=Object.create(null),n=0,s=e.params;n<s.length;n+=1){var i=s[n];this.checkLValInnerPattern(i,1,t?null:r)}},oe.parseExprList=function(e,t,r,n){for(var s=[],i=!0;!this.eat(e);){if(i)i=!1;else if(this.expect(_.comma),t&&this.afterTrailingComma(e))break;var a=void 0;r&&this.type===_.comma?a=null:this.type===_.ellipsis?(a=this.parseSpread(n),n&&this.type===_.comma&&n.trailingComma<0&&(n.trailingComma=this.start)):a=this.parseMaybeAssign(!1,n),s.push(a)}return s},oe.checkUnreserved=function(e){var t=e.start,r=e.end,n=e.name;this.inGenerator&&"yield"===n&&this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===n&&this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function"),this.currentThisScope().flags&U||"arguments"!==n||this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer"),!this.inClassStaticBlock||"arguments"!==n&&"await"!==n||this.raise(t,"Cannot use "+n+" in class static initialization block"),this.keywords.test(n)&&this.raise(t,"Unexpected keyword '"+n+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(t,r).indexOf("\\")||(this.strict?this.reservedWordsStrict:this.reservedWords).test(n)&&(this.inAsync||"await"!==n||this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(t,"The keyword '"+n+"' is reserved"))},oe.parseIdent=function(e){var t=this.parseIdentNode();return this.next(!!e),this.finishNode(t,"Identifier"),e||(this.checkUnreserved(t),"await"!==t.name||this.awaitIdentPos||(this.awaitIdentPos=t.start)),t},oe.parseIdentNode=function(){var e=this.startNode();return this.type===_.name?e.name=this.value:this.type.keyword?(e.name=this.type.keyword,"class"!==e.name&&"function"!==e.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop(),this.type=_.name):this.unexpected(),e},oe.parsePrivateIdent=function(){var e=this.startNode();return this.type===_.privateId?e.name=this.value:this.unexpected(),this.next(),this.finishNode(e,"PrivateIdentifier"),this.options.checkPrivateFields&&(0===this.privateNameStack.length?this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class"):this.privateNameStack[this.privateNameStack.length-1].used.push(e)),e},oe.parseYield=function(e){this.yieldPos||(this.yieldPos=this.start);var t=this.startNode();return this.next(),this.type===_.semi||this.canInsertSemicolon()||this.type!==_.star&&!this.type.startsExpr?(t.delegate=!1,t.argument=null):(t.delegate=this.eat(_.star),t.argument=this.parseMaybeAssign(e)),this.finishNode(t,"YieldExpression")},oe.parseAwait=function(e){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!0,!1,e),this.finishNode(t,"AwaitExpression")};var he=q.prototype;he.raise=function(e,t){var r=V(this.input,e);t+=" ("+r.line+":"+r.column+")",this.sourceFile&&(t+=" in "+this.sourceFile);var n=new SyntaxError(t);throw n.pos=e,n.loc=r,n.raisedAt=this.pos,n},he.raiseRecoverable=he.raise,he.curPosition=function(){if(this.options.locations)return new L(this.curLine,this.pos-this.lineStart)};var pe=q.prototype,de=function(e){this.flags=e,this.var=[],this.lexical=[],this.functions=[]};pe.enterScope=function(e){this.scopeStack.push(new de(e))},pe.exitScope=function(){this.scopeStack.pop()},pe.treatFunctionsAsVarInScope=function(e){return 2&e.flags||!this.inModule&&1&e.flags},pe.declareName=function(e,t,r){var n=!1;if(2===t){var s=this.currentScope();n=s.lexical.indexOf(e)>-1||s.functions.indexOf(e)>-1||s.var.indexOf(e)>-1,s.lexical.push(e),this.inModule&&1&s.flags&&delete this.undefinedExports[e]}else if(4===t)this.currentScope().lexical.push(e);else if(3===t){var i=this.currentScope();n=this.treatFunctionsAsVar?i.lexical.indexOf(e)>-1:i.lexical.indexOf(e)>-1||i.var.indexOf(e)>-1,i.functions.push(e)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(e)>-1&&!(32&o.flags&&o.lexical[0]===e)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(e)>-1){n=!0;break}if(o.var.push(e),this.inModule&&1&o.flags&&delete this.undefinedExports[e],o.flags&U)break}n&&this.raiseRecoverable(r,"Identifier '"+e+"' has already been declared")},pe.checkLocalExport=function(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&(this.undefinedExports[e.name]=e)},pe.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},pe.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(771&t.flags)return t}},pe.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(771&t.flags&&!(16&t.flags))return t}};var fe=function(e,t,r){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new D(e,r)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},ge=q.prototype;function me(e,t,r,n){return e.type=t,e.end=r,this.options.locations&&(e.loc.end=n),this.options.ranges&&(e.range[1]=r),e}ge.startNode=function(){return new fe(this,this.start,this.startLoc)},ge.startNodeAt=function(e,t){return new fe(this,e,t)},ge.finishNode=function(e,t){return me.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},ge.finishNodeAt=function(e,t,r,n){return me.call(this,e,t,r,n)},ge.copyNode=function(e){var t=new fe(this,e.start,this.startLoc);for(var r in e)t[r]=e[r];return t};var we="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",ye=we+" Extended_Pictographic",_e=ye+" EBase EComp EMod EPres ExtPict",Se={9:we,10:ye,11:ye,12:_e,13:_e,14:_e},ve={9:"",10:"",11:"",12:"",13:"",14:"Basic_Emoji Emoji_Keycap_Sequence RGI_Emoji_Modifier_Sequence RGI_Emoji_Flag_Sequence RGI_Emoji_Tag_Sequence RGI_Emoji_ZWJ_Sequence RGI_Emoji"},be="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",xe="Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",ke=xe+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",Ae=ke+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho",Ee=Ae+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi",Ce=Ee+" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith",Pe={9:xe,10:ke,11:Ae,12:Ee,13:Ce,14:Ce+" Gara Garay Gukh Gurung_Khema Hrkt Katakana_Or_Hiragana Kawi Kirat_Rai Krai Nag_Mundari Nagm Ol_Onal Onao Sunu Sunuwar Todhri Todr Tulu_Tigalari Tutg Unknown Zzzz"},Ie={};function Te(e){var t=Ie[e]={binary:R(Se[e]+" "+be),binaryOfStrings:R(ve[e]),nonBinary:{General_Category:R(be),Script:R(Pe[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script,t.nonBinary.gc=t.nonBinary.General_Category,t.nonBinary.sc=t.nonBinary.Script,t.nonBinary.scx=t.nonBinary.Script_Extensions}for(var Oe=0,Re=[9,10,11,12,13,14];Oe<Re.length;Oe+=1)Te(Re[Oe]);var Ne=q.prototype,je=function(e,t){this.parent=e,this.base=t||this};je.prototype.separatedFrom=function(e){for(var t=this;t;t=t.parent)for(var r=e;r;r=r.parent)if(t.base===r.base&&t!==r)return!0;return!1},je.prototype.sibling=function(){return new je(this.parent,this.base)};var Le=function(e){this.parser=e,this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"")+(e.options.ecmaVersion>=13?"d":"")+(e.options.ecmaVersion>=15?"v":""),this.unicodeProperties=Ie[e.options.ecmaVersion>=14?14:e.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchV=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=Object.create(null),this.backReferenceNames=[],this.branchID=null};function De(e){return 105===e||109===e||115===e}function Ve(e){return 36===e||e>=40&&e<=43||46===e||63===e||e>=91&&e<=94||e>=123&&e<=125}function Me(e){return e>=65&&e<=90||e>=97&&e<=122}Le.prototype.reset=function(e,t,r){var n=-1!==r.indexOf("v"),s=-1!==r.indexOf("u");this.start=0|e,this.source=t+"",this.flags=r,n&&this.parser.options.ecmaVersion>=15?(this.switchU=!0,this.switchV=!0,this.switchN=!0):(this.switchU=s&&this.parser.options.ecmaVersion>=6,this.switchV=!1,this.switchN=s&&this.parser.options.ecmaVersion>=9)},Le.prototype.raise=function(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)},Le.prototype.at=function(e,t){void 0===t&&(t=!1);var r=this.source,n=r.length;if(e>=n)return-1;var s=r.charCodeAt(e);if(!t&&!this.switchU||s<=55295||s>=57344||e+1>=n)return s;var i=r.charCodeAt(e+1);return i>=56320&&i<=57343?(s<<10)+i-56613888:s},Le.prototype.nextIndex=function(e,t){void 0===t&&(t=!1);var r=this.source,n=r.length;if(e>=n)return n;var s,i=r.charCodeAt(e);return!t&&!this.switchU||i<=55295||i>=57344||e+1>=n||(s=r.charCodeAt(e+1))<56320||s>57343?e+1:e+2},Le.prototype.current=function(e){return void 0===e&&(e=!1),this.at(this.pos,e)},Le.prototype.lookahead=function(e){return void 0===e&&(e=!1),this.at(this.nextIndex(this.pos,e),e)},Le.prototype.advance=function(e){void 0===e&&(e=!1),this.pos=this.nextIndex(this.pos,e)},Le.prototype.eat=function(e,t){return void 0===t&&(t=!1),this.current(t)===e&&(this.advance(t),!0)},Le.prototype.eatChars=function(e,t){void 0===t&&(t=!1);for(var r=this.pos,n=0,s=e;n<s.length;n+=1){var i=s[n],a=this.at(r,t);if(-1===a||a!==i)return!1;r=this.nextIndex(r,t)}return this.pos=r,!0},Ne.validateRegExpFlags=function(e){for(var t=e.validFlags,r=e.flags,n=!1,s=!1,i=0;i<r.length;i++){var a=r.charAt(i);-1===t.indexOf(a)&&this.raise(e.start,"Invalid regular expression flag"),r.indexOf(a,i+1)>-1&&this.raise(e.start,"Duplicate regular expression flag"),"u"===a&&(n=!0),"v"===a&&(s=!0)}this.options.ecmaVersion>=15&&n&&s&&this.raise(e.start,"Invalid regular expression flag")},Ne.validateRegExpPattern=function(e){this.regexp_pattern(e),!e.switchN&&this.options.ecmaVersion>=9&&function(e){for(var t in e)return!0;return!1}(e.groupNames)&&(e.switchN=!0,this.regexp_pattern(e))},Ne.regexp_pattern=function(e){e.pos=0,e.lastIntValue=0,e.lastStringValue="",e.lastAssertionIsQuantifiable=!1,e.numCapturingParens=0,e.maxBackReference=0,e.groupNames=Object.create(null),e.backReferenceNames.length=0,e.branchID=null,this.regexp_disjunction(e),e.pos!==e.source.length&&(e.eat(41)&&e.raise("Unmatched ')'"),(e.eat(93)||e.eat(125))&&e.raise("Lone quantifier brackets")),e.maxBackReference>e.numCapturingParens&&e.raise("Invalid escape");for(var t=0,r=e.backReferenceNames;t<r.length;t+=1){var n=r[t];e.groupNames[n]||e.raise("Invalid named capture referenced")}},Ne.regexp_disjunction=function(e){var t=this.options.ecmaVersion>=16;for(t&&(e.branchID=new je(e.branchID,null)),this.regexp_alternative(e);e.eat(124);)t&&(e.branchID=e.branchID.sibling()),this.regexp_alternative(e);t&&(e.branchID=e.branchID.parent),this.regexp_eatQuantifier(e,!0)&&e.raise("Nothing to repeat"),e.eat(123)&&e.raise("Lone quantifier brackets")},Ne.regexp_alternative=function(e){for(;e.pos<e.source.length&&this.regexp_eatTerm(e););},Ne.regexp_eatTerm=function(e){return this.regexp_eatAssertion(e)?(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)&&e.switchU&&e.raise("Invalid quantifier"),!0):!!(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e))&&(this.regexp_eatQuantifier(e),!0)},Ne.regexp_eatAssertion=function(e){var t=e.pos;if(e.lastAssertionIsQuantifiable=!1,e.eat(94)||e.eat(36))return!0;if(e.eat(92)){if(e.eat(66)||e.eat(98))return!0;e.pos=t}if(e.eat(40)&&e.eat(63)){var r=!1;if(this.options.ecmaVersion>=9&&(r=e.eat(60)),e.eat(61)||e.eat(33))return this.regexp_disjunction(e),e.eat(41)||e.raise("Unterminated group"),e.lastAssertionIsQuantifiable=!r,!0}return e.pos=t,!1},Ne.regexp_eatQuantifier=function(e,t){return void 0===t&&(t=!1),!!this.regexp_eatQuantifierPrefix(e,t)&&(e.eat(63),!0)},Ne.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)},Ne.regexp_eatBracedQuantifier=function(e,t){var r=e.pos;if(e.eat(123)){var n=0,s=-1;if(this.regexp_eatDecimalDigits(e)&&(n=e.lastIntValue,e.eat(44)&&this.regexp_eatDecimalDigits(e)&&(s=e.lastIntValue),e.eat(125)))return-1!==s&&s<n&&!t&&e.raise("numbers out of order in {} quantifier"),!0;e.switchU&&!t&&e.raise("Incomplete quantifier"),e.pos=r}return!1},Ne.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)},Ne.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e))return!0;e.pos=t}return!1},Ne.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)){if(this.options.ecmaVersion>=16){var r=this.regexp_eatModifiers(e),n=e.eat(45);if(r||n){for(var s=0;s<r.length;s++){var i=r.charAt(s);r.indexOf(i,s+1)>-1&&e.raise("Duplicate regular expression modifiers")}if(n){var a=this.regexp_eatModifiers(e);r||a||58!==e.current()||e.raise("Invalid regular expression modifiers");for(var o=0;o<a.length;o++){var c=a.charAt(o);(a.indexOf(c,o+1)>-1||r.indexOf(c)>-1)&&e.raise("Duplicate regular expression modifiers")}}}}if(e.eat(58)){if(this.regexp_disjunction(e),e.eat(41))return!0;e.raise("Unterminated group")}}e.pos=t}return!1},Ne.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(e):63===e.current()&&e.raise("Invalid group"),this.regexp_disjunction(e),e.eat(41))return e.numCapturingParens+=1,!0;e.raise("Unterminated group")}return!1},Ne.regexp_eatModifiers=function(e){for(var t="",r=0;-1!==(r=e.current())&&De(r);)t+=N(r),e.advance();return t},Ne.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)},Ne.regexp_eatInvalidBracedQuantifier=function(e){return this.regexp_eatBracedQuantifier(e,!0)&&e.raise("Nothing to repeat"),!1},Ne.regexp_eatSyntaxCharacter=function(e){var t=e.current();return!!Ve(t)&&(e.lastIntValue=t,e.advance(),!0)},Ne.regexp_eatPatternCharacters=function(e){for(var t=e.pos,r=0;-1!==(r=e.current())&&!Ve(r);)e.advance();return e.pos!==t},Ne.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();return!(-1===t||36===t||t>=40&&t<=43||46===t||63===t||91===t||94===t||124===t||(e.advance(),0))},Ne.regexp_groupSpecifier=function(e){if(e.eat(63)){this.regexp_eatGroupName(e)||e.raise("Invalid group");var t=this.options.ecmaVersion>=16,r=e.groupNames[e.lastStringValue];if(r)if(t)for(var n=0,s=r;n<s.length;n+=1)s[n].separatedFrom(e.branchID)||e.raise("Duplicate capture group name");else e.raise("Duplicate capture group name");t?(r||(e.groupNames[e.lastStringValue]=[])).push(e.branchID):e.groupNames[e.lastStringValue]=!0}},Ne.regexp_eatGroupName=function(e){if(e.lastStringValue="",e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62))return!0;e.raise("Invalid capture group name")}return!1},Ne.regexp_eatRegExpIdentifierName=function(e){if(e.lastStringValue="",this.regexp_eatRegExpIdentifierStart(e)){for(e.lastStringValue+=N(e.lastIntValue);this.regexp_eatRegExpIdentifierPart(e);)e.lastStringValue+=N(e.lastIntValue);return!0}return!1},Ne.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos,r=this.options.ecmaVersion>=11,n=e.current(r);return e.advance(r),92===n&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)&&(n=e.lastIntValue),function(e){return h(e,!0)||36===e||95===e}(n)?(e.lastIntValue=n,!0):(e.pos=t,!1)},Ne.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos,r=this.options.ecmaVersion>=11,n=e.current(r);return e.advance(r),92===n&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)&&(n=e.lastIntValue),function(e){return p(e,!0)||36===e||95===e||8204===e||8205===e}(n)?(e.lastIntValue=n,!0):(e.pos=t,!1)},Ne.regexp_eatAtomEscape=function(e){return!!(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e))||(e.switchU&&(99===e.current()&&e.raise("Invalid unicode escape"),e.raise("Invalid escape")),!1)},Ne.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var r=e.lastIntValue;if(e.switchU)return r>e.maxBackReference&&(e.maxBackReference=r),!0;if(r<=e.numCapturingParens)return!0;e.pos=t}return!1},Ne.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e))return e.backReferenceNames.push(e.lastStringValue),!0;e.raise("Invalid named reference")}return!1},Ne.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,!1)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)},Ne.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e))return!0;e.pos=t}return!1},Ne.regexp_eatZero=function(e){return 48===e.current()&&!$e(e.lookahead())&&(e.lastIntValue=0,e.advance(),!0)},Ne.regexp_eatControlEscape=function(e){var t=e.current();return 116===t?(e.lastIntValue=9,e.advance(),!0):110===t?(e.lastIntValue=10,e.advance(),!0):118===t?(e.lastIntValue=11,e.advance(),!0):102===t?(e.lastIntValue=12,e.advance(),!0):114===t&&(e.lastIntValue=13,e.advance(),!0)},Ne.regexp_eatControlLetter=function(e){var t=e.current();return!!Me(t)&&(e.lastIntValue=t%32,e.advance(),!0)},Ne.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){void 0===t&&(t=!1);var r,n=e.pos,s=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var i=e.lastIntValue;if(s&&i>=55296&&i<=56319){var a=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var o=e.lastIntValue;if(o>=56320&&o<=57343)return e.lastIntValue=1024*(i-55296)+(o-56320)+65536,!0}e.pos=a,e.lastIntValue=i}return!0}if(s&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&((r=e.lastIntValue)>=0&&r<=1114111))return!0;s&&e.raise("Invalid unicode escape"),e.pos=n}return!1},Ne.regexp_eatIdentityEscape=function(e){if(e.switchU)return!!this.regexp_eatSyntaxCharacter(e)||!!e.eat(47)&&(e.lastIntValue=47,!0);var t=e.current();return!(99===t||e.switchN&&107===t||(e.lastIntValue=t,e.advance(),0))},Ne.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48),e.advance()}while((t=e.current())>=48&&t<=57);return!0}return!1};function Be(e){return Me(e)||95===e}function Fe(e){return Be(e)||$e(e)}function $e(e){return e>=48&&e<=57}function Ue(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function We(e){return e>=65&&e<=70?e-65+10:e>=97&&e<=102?e-97+10:e-48}function qe(e){return e>=48&&e<=55}Ne.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(function(e){return 100===e||68===e||115===e||83===e||119===e||87===e}(t))return e.lastIntValue=-1,e.advance(),1;var r=!1;if(e.switchU&&this.options.ecmaVersion>=9&&((r=80===t)||112===t)){var n;if(e.lastIntValue=-1,e.advance(),e.eat(123)&&(n=this.regexp_eatUnicodePropertyValueExpression(e))&&e.eat(125))return r&&2===n&&e.raise("Invalid property name"),n;e.raise("Invalid property name")}return 0},Ne.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var r=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var n=e.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(e,r,n),1}}if(e.pos=t,this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var s=e.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(e,s)}return 0},Ne.regexp_validateUnicodePropertyNameAndValue=function(e,t,r){I(e.unicodeProperties.nonBinary,t)||e.raise("Invalid property name"),e.unicodeProperties.nonBinary[t].test(r)||e.raise("Invalid property value")},Ne.regexp_validateUnicodePropertyNameOrValue=function(e,t){return e.unicodeProperties.binary.test(t)?1:e.switchV&&e.unicodeProperties.binaryOfStrings.test(t)?2:void e.raise("Invalid property name")},Ne.regexp_eatUnicodePropertyName=function(e){var t=0;for(e.lastStringValue="";Be(t=e.current());)e.lastStringValue+=N(t),e.advance();return""!==e.lastStringValue},Ne.regexp_eatUnicodePropertyValue=function(e){var t=0;for(e.lastStringValue="";Fe(t=e.current());)e.lastStringValue+=N(t),e.advance();return""!==e.lastStringValue},Ne.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)},Ne.regexp_eatCharacterClass=function(e){if(e.eat(91)){var t=e.eat(94),r=this.regexp_classContents(e);return e.eat(93)||e.raise("Unterminated character class"),t&&2===r&&e.raise("Negated character class may contain strings"),!0}return!1},Ne.regexp_classContents=function(e){return 93===e.current()?1:e.switchV?this.regexp_classSetExpression(e):(this.regexp_nonEmptyClassRanges(e),1)},Ne.regexp_nonEmptyClassRanges=function(e){for(;this.regexp_eatClassAtom(e);){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var r=e.lastIntValue;!e.switchU||-1!==t&&-1!==r||e.raise("Invalid character class"),-1!==t&&-1!==r&&t>r&&e.raise("Range out of order in character class")}}},Ne.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e))return!0;if(e.switchU){var r=e.current();(99===r||qe(r))&&e.raise("Invalid class escape"),e.raise("Invalid escape")}e.pos=t}var n=e.current();return 93!==n&&(e.lastIntValue=n,e.advance(),!0)},Ne.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98))return e.lastIntValue=8,!0;if(e.switchU&&e.eat(45))return e.lastIntValue=45,!0;if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e))return!0;e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)},Ne.regexp_classSetExpression=function(e){var t,r=1;if(this.regexp_eatClassSetRange(e));else if(t=this.regexp_eatClassSetOperand(e)){2===t&&(r=2);for(var n=e.pos;e.eatChars([38,38]);)38!==e.current()&&(t=this.regexp_eatClassSetOperand(e))?2!==t&&(r=1):e.raise("Invalid character in character class");if(n!==e.pos)return r;for(;e.eatChars([45,45]);)this.regexp_eatClassSetOperand(e)||e.raise("Invalid character in character class");if(n!==e.pos)return r}else e.raise("Invalid character in character class");for(;;)if(!this.regexp_eatClassSetRange(e)){if(!(t=this.regexp_eatClassSetOperand(e)))return r;2===t&&(r=2)}},Ne.regexp_eatClassSetRange=function(e){var t=e.pos;if(this.regexp_eatClassSetCharacter(e)){var r=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassSetCharacter(e)){var n=e.lastIntValue;return-1!==r&&-1!==n&&r>n&&e.raise("Range out of order in character class"),!0}e.pos=t}return!1},Ne.regexp_eatClassSetOperand=function(e){return this.regexp_eatClassSetCharacter(e)?1:this.regexp_eatClassStringDisjunction(e)||this.regexp_eatNestedClass(e)},Ne.regexp_eatNestedClass=function(e){var t=e.pos;if(e.eat(91)){var r=e.eat(94),n=this.regexp_classContents(e);if(e.eat(93))return r&&2===n&&e.raise("Negated character class may contain strings"),n;e.pos=t}if(e.eat(92)){var s=this.regexp_eatCharacterClassEscape(e);if(s)return s;e.pos=t}return null},Ne.regexp_eatClassStringDisjunction=function(e){var t=e.pos;if(e.eatChars([92,113])){if(e.eat(123)){var r=this.regexp_classStringDisjunctionContents(e);if(e.eat(125))return r}else e.raise("Invalid escape");e.pos=t}return null},Ne.regexp_classStringDisjunctionContents=function(e){for(var t=this.regexp_classString(e);e.eat(124);)2===this.regexp_classString(e)&&(t=2);return t},Ne.regexp_classString=function(e){for(var t=0;this.regexp_eatClassSetCharacter(e);)t++;return 1===t?1:2},Ne.regexp_eatClassSetCharacter=function(e){var t=e.pos;if(e.eat(92))return!(!this.regexp_eatCharacterEscape(e)&&!this.regexp_eatClassSetReservedPunctuator(e)&&(e.eat(98)?(e.lastIntValue=8,0):(e.pos=t,1)));var r=e.current();return!(r<0||r===e.lookahead()&&function(e){return 33===e||e>=35&&e<=38||e>=42&&e<=44||46===e||e>=58&&e<=64||94===e||96===e||126===e}(r)||function(e){return 40===e||41===e||45===e||47===e||e>=91&&e<=93||e>=123&&e<=125}(r)||(e.advance(),e.lastIntValue=r,0))},Ne.regexp_eatClassSetReservedPunctuator=function(e){var t=e.current();return!!function(e){return 33===e||35===e||37===e||38===e||44===e||45===e||e>=58&&e<=62||64===e||96===e||126===e}(t)&&(e.lastIntValue=t,e.advance(),!0)},Ne.regexp_eatClassControlLetter=function(e){var t=e.current();return!(!$e(t)&&95!==t||(e.lastIntValue=t%32,e.advance(),0))},Ne.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2))return!0;e.switchU&&e.raise("Invalid escape"),e.pos=t}return!1},Ne.regexp_eatDecimalDigits=function(e){var t=e.pos,r=0;for(e.lastIntValue=0;$e(r=e.current());)e.lastIntValue=10*e.lastIntValue+(r-48),e.advance();return e.pos!==t},Ne.regexp_eatHexDigits=function(e){var t=e.pos,r=0;for(e.lastIntValue=0;Ue(r=e.current());)e.lastIntValue=16*e.lastIntValue+We(r),e.advance();return e.pos!==t},Ne.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var r=e.lastIntValue;t<=3&&this.regexp_eatOctalDigit(e)?e.lastIntValue=64*t+8*r+e.lastIntValue:e.lastIntValue=8*t+r}else e.lastIntValue=t;return!0}return!1},Ne.regexp_eatOctalDigit=function(e){var t=e.current();return qe(t)?(e.lastIntValue=t-48,e.advance(),!0):(e.lastIntValue=0,!1)},Ne.regexp_eatFixedHexDigits=function(e,t){var r=e.pos;e.lastIntValue=0;for(var n=0;n<t;++n){var s=e.current();if(!Ue(s))return e.pos=r,!1;e.lastIntValue=16*e.lastIntValue+We(s),e.advance()}return!0};var He=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new D(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},Ke=q.prototype;function ze(e){return"function"!=typeof BigInt?null:BigInt(e.replace(/_/g,""))}Ke.next=function(e){!e&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new He(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},Ke.getToken=function(){return this.next(),new He(this)},"undefined"!=typeof Symbol&&(Ke[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===_.eof,value:t}}}}),Ke.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(_.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},Ke.readToken=function(e){return h(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},Ke.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320)return e;var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888},Ke.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,r=this.input.indexOf("*/",this.pos+=2);if(-1===r&&this.raise(this.pos-2,"Unterminated comment"),this.pos=r+2,this.options.locations)for(var n=void 0,s=t;(n=x(this.input,s,this.pos))>-1;)++this.curLine,s=this.lineStart=n;this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,r),t,this.pos,e,this.curPosition())},Ke.skipLineComment=function(e){for(var t=this.pos,r=this.options.onComment&&this.curPosition(),n=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!b(n);)n=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,r,this.curPosition())},Ke.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&k.test(String.fromCharCode(e))))break e;++this.pos}}},Ke.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var r=this.type;this.type=e,this.value=t,this.updateContext(r)},Ke.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(_.ellipsis)):(++this.pos,this.finishToken(_.dot))},Ke.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(_.assign,2):this.finishOp(_.slash,1)},Ke.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),r=1,n=42===e?_.star:_.modulo;return this.options.ecmaVersion>=7&&42===e&&42===t&&(++r,n=_.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(_.assign,r+1):this.finishOp(n,r)},Ke.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?this.options.ecmaVersion>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(_.assign,3):this.finishOp(124===e?_.logicalOR:_.logicalAND,2):61===t?this.finishOp(_.assign,2):this.finishOp(124===e?_.bitwiseOR:_.bitwiseAND,1)},Ke.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(_.assign,2):this.finishOp(_.bitwiseXOR,1)},Ke.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!==t||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!S.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(_.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(_.assign,2):this.finishOp(_.plusMin,1)},Ke.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),r=1;return t===e?(r=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+r)?this.finishOp(_.assign,r+1):this.finishOp(_.bitShift,r)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===t&&(r=2),this.finishOp(_.relational,r)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},Ke.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(_.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(_.arrow)):this.finishOp(61===e?_.eq:_.prefix,1)},Ke.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(46===t){var r=this.input.charCodeAt(this.pos+2);if(r<48||r>57)return this.finishOp(_.questionDot,2)}if(63===t)return e>=12&&61===this.input.charCodeAt(this.pos+2)?this.finishOp(_.assign,3):this.finishOp(_.coalesce,2)}return this.finishOp(_.question,1)},Ke.readToken_numberSign=function(){var e=35;if(this.options.ecmaVersion>=13&&(++this.pos,h(e=this.fullCharCodeAtPos(),!0)||92===e))return this.finishToken(_.privateId,this.readWord1());this.raise(this.pos,"Unexpected character '"+N(e)+"'")},Ke.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(_.parenL);case 41:return++this.pos,this.finishToken(_.parenR);case 59:return++this.pos,this.finishToken(_.semi);case 44:return++this.pos,this.finishToken(_.comma);case 91:return++this.pos,this.finishToken(_.bracketL);case 93:return++this.pos,this.finishToken(_.bracketR);case 123:return++this.pos,this.finishToken(_.braceL);case 125:return++this.pos,this.finishToken(_.braceR);case 58:return++this.pos,this.finishToken(_.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(_.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(_.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+N(e)+"'")},Ke.finishOp=function(e,t){var r=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,r)},Ke.readRegexp=function(){for(var e,t,r=this.pos;;){this.pos>=this.input.length&&this.raise(r,"Unterminated regular expression");var n=this.input.charAt(this.pos);if(S.test(n)&&this.raise(r,"Unterminated regular expression"),e)e=!1;else{if("["===n)t=!0;else if("]"===n&&t)t=!1;else if("/"===n&&!t)break;e="\\"===n}++this.pos}var s=this.input.slice(r,this.pos);++this.pos;var i=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(i);var o=this.regexpState||(this.regexpState=new Le(this));o.reset(r,s,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var c=null;try{c=new RegExp(s,a)}catch(e){}return this.finishToken(_.regexp,{pattern:s,flags:a,value:c})},Ke.readInt=function(e,t,r){for(var n=this.options.ecmaVersion>=12&&void 0===t,s=r&&48===this.input.charCodeAt(this.pos),i=this.pos,a=0,o=0,c=0,l=null==t?1/0:t;c<l;++c,++this.pos){var u=this.input.charCodeAt(this.pos),h=void 0;if(n&&95===u)s&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===c&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=u;else{if((h=u>=97?u-97+10:u>=65?u-65+10:u>=48&&u<=57?u-48:1/0)>=e)break;o=u,a=a*e+h}}return n&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===i||null!=t&&this.pos-i!==t?null:a},Ke.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var r=this.readInt(e);return null==r&&this.raise(this.start+2,"Expected number in radix "+e),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(r=ze(this.input.slice(t,this.pos)),++this.pos):h(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(_.num,r)},Ke.readNumber=function(e){var t=this.pos;e||null!==this.readInt(10,void 0,!0)||this.raise(t,"Invalid number");var r=this.pos-t>=2&&48===this.input.charCodeAt(t);r&&this.strict&&this.raise(t,"Invalid number");var n=this.input.charCodeAt(this.pos);if(!r&&!e&&this.options.ecmaVersion>=11&&110===n){var s=ze(this.input.slice(t,this.pos));return++this.pos,h(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(_.num,s)}r&&/[89]/.test(this.input.slice(t,this.pos))&&(r=!1),46!==n||r||(++this.pos,this.readInt(10),n=this.input.charCodeAt(this.pos)),69!==n&&101!==n||r||(43!==(n=this.input.charCodeAt(++this.pos))&&45!==n||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number")),h(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var i,a=(i=this.input.slice(t,this.pos),r?parseInt(i,8):parseFloat(i.replace(/_/g,"")));return this.finishToken(_.num,a)},Ke.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},Ke.readString=function(e){for(var t="",r=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var n=this.input.charCodeAt(this.pos);if(n===e)break;92===n?(t+=this.input.slice(r,this.pos),t+=this.readEscapedChar(!1),r=this.pos):8232===n||8233===n?(this.options.ecmaVersion<10&&this.raise(this.start,"Unterminated string constant"),++this.pos,this.options.locations&&(this.curLine++,this.lineStart=this.pos)):(b(n)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(r,this.pos++),this.finishToken(_.string,t)};var Je={};Ke.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==Je)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},Ke.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Je;this.raise(e,t)},Ke.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var r=this.input.charCodeAt(this.pos);if(96===r||36===r&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==_.template&&this.type!==_.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(_.template,e)):36===r?(this.pos+=2,this.finishToken(_.dollarBraceL)):(++this.pos,this.finishToken(_.backQuote));if(92===r)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(b(r)){switch(e+=this.input.slice(t,this.pos),++this.pos,r){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(r)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},Ke.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(_.invalidTemplate,this.input.slice(this.start,this.pos));case"\r":"\n"===this.input[this.pos+1]&&++this.pos;case"\n":case"\u2028":case"\u2029":++this.curLine,this.lineStart=this.pos+1}this.raise(this.start,"Unterminated template")},Ke.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return N(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(this.strict&&this.invalidStringToken(this.pos-1,"Invalid escape sequence"),e){var r=this.pos-1;this.invalidStringToken(r,"Invalid escape sequence in template string")}default:if(t>=48&&t<=55){var n=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],s=parseInt(n,8);return s>255&&(n=n.slice(0,-1),s=parseInt(n,8)),this.pos+=n.length-1,t=this.input.charCodeAt(this.pos),"0"===n&&56!==t&&57!==t||!this.strict&&!e||this.invalidStringToken(this.pos-1-n.length,e?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(s)}return b(t)?(this.options.locations&&(this.lineStart=this.pos,++this.curLine),""):String.fromCharCode(t)}},Ke.readHexChar=function(e){var t=this.pos,r=this.readInt(16,e);return null===r&&this.invalidStringToken(t,"Bad character escape sequence"),r},Ke.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,r=this.pos,n=this.options.ecmaVersion>=6;this.pos<this.input.length;){var s=this.fullCharCodeAtPos();if(p(s,n))this.pos+=s<=65535?1:2;else{if(92!==s)break;this.containsEsc=!0,e+=this.input.slice(r,this.pos);var i=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(t?h:p)(a,n)||this.invalidStringToken(i,"Invalid Unicode escape"),e+=N(a),r=this.pos}t=!1}return e+this.input.slice(r,this.pos)},Ke.readWord=function(){var e=this.readWord1(),t=_.name;return this.keywords.test(e)&&(t=w[e]),this.finishToken(t,e)};var Ge="8.15.0";q.acorn={Parser:q,version:Ge,defaultOptions:M,Position:L,SourceLocation:D,getLineInfo:V,Node:fe,TokenType:d,tokTypes:_,keywordTypes:w,TokContext:se,tokContexts:ie,isIdentifierChar:p,isIdentifierStart:h,Token:He,isNewLine:b,lineBreak:S,lineBreakG:v,nonASCIIwhitespace:k},e.Node=fe,e.Parser=q,e.Position=L,e.SourceLocation=D,e.TokContext=se,e.Token=He,e.TokenType=d,e.defaultOptions=M,e.getLineInfo=V,e.isIdentifierChar=p,e.isIdentifierStart=h,e.isNewLine=b,e.keywordTypes=w,e.lineBreak=S,e.lineBreakG=v,e.nonASCIIwhitespace=k,e.parse=function(e,t){return q.parse(e,t)},e.parseExpressionAt=function(e,t,r){return q.parseExpressionAt(e,t,r)},e.tokContexts=ie,e.tokTypes=_,e.tokenizer=function(e,t){return q.tokenizer(e,t)},e.version=Ge}(t)}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.amdO={},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){var e,r,s="function"==typeof Symbol?Symbol:{},i=s.iterator||"@@iterator",a=s.toStringTag||"@@toStringTag";function o(t,s,i,a){var o=s&&s.prototype instanceof l?s:l,u=Object.create(o.prototype);return n(u,"_invoke",function(t,n,s){var i,a,o,l=0,u=s||[],h=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,r){return i=t,a=0,o=e,p.n=r,c}};function d(t,n){for(a=t,o=n,r=0;!h&&l&&!s&&r<u.length;r++){var s,i=u[r],d=p.p,f=i[2];t>3?(s=f===n)&&(o=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=d&&((s=t<2&&d<i[1])?(a=0,p.v=n,p.n=i[1]):d<f&&(s=t<3||i[0]>n||n>f)&&(i[4]=t,i[5]=n,p.n=f,a=0))}if(s||t>1)return c;throw h=!0,n}return function(s,u,f){if(l>1)throw TypeError("Generator is already running");for(h&&1===u&&d(u,f),a=u,o=f;(r=a<2?e:o)||!h;){i||(a?a<3?(a>1&&(p.n=-1),d(a,o)):p.n=o:p.v=o);try{if(l=2,i){if(a||(s="next"),r=i[s]){if(!(r=r.call(i,o)))throw TypeError("iterator result is not an object");if(!r.done)return r;o=r.value,a<2&&(a=0)}else 1===a&&(r=i.return)&&r.call(i),a<2&&(o=TypeError("The iterator does not provide a '"+s+"' method"),a=1);i=e}else if((r=(h=p.n<0)?o:t.call(n,p))!==c)break}catch(t){i=e,a=1,o=t}finally{l=1}}return{value:r,done:h}}}(t,i,a),!0),u}var c={};function l(){}function u(){}function h(){}r=Object.getPrototypeOf;var p=[][i]?r(r([][i]())):(n(r={},i,function(){return this}),r),d=h.prototype=l.prototype=Object.create(p);function f(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,n(e,a,"GeneratorFunction")),e.prototype=Object.create(d),e}return u.prototype=h,n(d,"constructor",h),n(h,"constructor",u),u.displayName="GeneratorFunction",n(h,a,"GeneratorFunction"),n(d),n(d,a,"Generator"),n(d,i,function(){return this}),n(d,"toString",function(){return"[object Generator]"}),(t=function(){return{w:o,m:f}})()}function n(e,t,r,s){var i=Object.defineProperty;try{i({},"",{})}catch(e){i=0}n=function(e,t,r,s){if(t)i?i(e,t,{value:r,enumerable:!s,configurable:!s,writable:!s}):e[t]=r;else{var a=function(t,r){n(e,t,function(e){return this._invoke(t,r,e)})};a("next",0),a("throw",1),a("return",2)}},n(e,t,r,s)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){a(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function a(t,r,n){return(r=function(t){var r=function(t){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}function o(e,t,r,n,s,i,a){try{var o=e[i](a),c=o.value}catch(e){return void r(e)}o.done?t(c):Promise.resolve(c).then(n,s)}var c=r(630),l=r(268),u=[];function h(e,t,r){var n=e.start,s=e.end,i=t.slice(n,s);/webpackChunkName:/.test(i)&&r.frameworkHints.add("webpack")}function p(e,t,r){"MemberExpression"===e.callee.type&&"require"===e.callee.object.name&&"ensure"===e.callee.property.name&&(r.suspiciousPatterns.push("require.ensure"),r.frameworkHints.add("webpack"),u.push(e.end))}function d(e,t,r){var n,s;"createElement"===(null===(n=e.callee.property)||void 0===n?void 0:n.name)&&"script"===(null===(s=e.arguments[0])||void 0===s?void 0:s.value)&&(r.suspiciousPatterns.push("createElement(script)"),u.push(e.end))}function f(e,t,r){var n=t.slice(e.start,e.end);/React\.lazy\(.*=>\s*import\(/.test(n)&&r.frameworkHints.add("react"),/defineAsyncComponent\(.*import\(/.test(n)&&r.frameworkHints.add("vue")}function g(){return g=function(e){return function(){var t=this,r=arguments;return new Promise(function(n,s){var i=e.apply(t,r);function a(e){o(i,n,s,a,c,"next",e)}function c(e){o(i,n,s,a,c,"throw",e)}a(void 0)})}}(t().m(function e(r){var n,s;return t().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,n=c.parse(r,{ecmaVersion:2022,sourceType:"module",locations:!0,ranges:!0}),s={dynamicImport:!1,frameworkHints:new Set,suspiciousPatterns:[],suspiciousCodes:new Set},l.full(n,function(e){"ImportExpression"===e.type&&(s.dynamicImport=!0,h(e,r,s),u.push(e.start)),"CallExpression"===e.type&&(p(e,0,s),d(e,0,s),f(e,r,s))}),u.forEach(function(e){["FunctionExpression"].forEach(function(t){var i=l.findNodeAfter(n,e,function(e,r){return e===t});if(i){var a=r.slice(i.node.start,i.node.end);a.includes(".js")&&s.suspiciousCodes.add(a)}})}),e.a(2,i(i({},s),{},{frameworkHints:Array.from(s.frameworkHints),isDynamicLoading:s.dynamicImport||s.suspiciousPatterns.length>0}));case 1:return e.p=1,e.v,e.a(2,{error:!0})}},e,null,[[0,1]])})),g.apply(this,arguments)}const m="RFC3986",w={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},y=(Object.prototype.hasOwnProperty,Array.isArray),_=(()=>{const e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})(),S=1024;function v(e,t){if(y(e)){const r=[];for(let n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)}const b=Object.prototype.hasOwnProperty,x={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},k=Array.isArray,A=Array.prototype.push,E=function(e,t){A.apply(e,k(t)?t:[t])},C=Date.prototype.toISOString,P={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,r,n,s)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=S){const t=i.length>=S?i.slice(e,e+S):i,r=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===s&&(40===n||41===n)?r[r.length]=t.charAt(e):n<128?r[r.length]=_[n]:n<2048?r[r.length]=_[192|n>>6]+_[128|63&n]:n<55296||n>=57344?r[r.length]=_[224|n>>12]+_[128|n>>6&63]+_[128|63&n]:(e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r[r.length]=_[240|n>>18]+_[128|n>>12&63]+_[128|n>>6&63]+_[128|63&n])}a+=r.join("")}return a},encodeValuesOnly:!1,format:m,formatter:w[m],indices:!1,serializeDate:e=>C.call(e),skipNulls:!1,strictNullHandling:!1},I={};function T(e,t,r,n,s,i,a,o,c,l,u,h,p,d,f,g,m,w){let y=e,_=w,S=0,b=!1;for(;void 0!==(_=_.get(I))&&!b;){const t=_.get(e);if(S+=1,void 0!==t){if(t===S)throw new RangeError("Cyclic object value");b=!0}void 0===_.get(I)&&(S=0)}if("function"==typeof l?y=l(t,y):y instanceof Date?y=p?.(y):"comma"===r&&k(y)&&(y=v(y,function(e){return e instanceof Date?p?.(e):e})),null===y){if(i)return c&&!g?c(t,P.encoder,m,"key",d):t;y=""}if(function(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e||"symbol"==typeof e||"bigint"==typeof e}(y)||function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))}(y)){if(c){const e=g?t:c(t,P.encoder,m,"key",d);return[f?.(e)+"="+f?.(c(y,P.encoder,m,"value",d))]}return[f?.(t)+"="+f?.(String(y))]}const x=[];if(void 0===y)return x;let A;if("comma"===r&&k(y))g&&c&&(y=v(y,c)),A=[{value:y.length>0?y.join(",")||null:void 0}];else if(k(l))A=l;else{const e=Object.keys(y);A=u?e.sort(u):e}const C=o?String(t).replace(/\./g,"%2E"):String(t),O=n&&k(y)&&1===y.length?C+"[]":C;if(s&&k(y)&&0===y.length)return O+"[]";for(let t=0;t<A.length;++t){const _=A[t],v="object"==typeof _&&void 0!==_.value?_.value:y[_];if(a&&null===v)continue;const b=h&&o?_.replace(/\./g,"%2E"):_,C=k(y)?"function"==typeof r?r(O,b):O:O+(h?"."+b:"["+b+"]");w.set(e,S);const P=new WeakMap;P.set(I,w),E(x,T(v,C,r,n,s,i,a,o,"comma"===r&&g&&k(y)?null:c,l,u,h,p,d,f,g,m,P))}return x}const O="4.104.0";let R,N,j,L,D,V,M,B,F,$=!1,U=null,W=null,q=null,H=null;class K{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}const z=()=>{R||function(e,t={auto:!1}){if($)throw new Error(`you must \`import 'openai/shims/${e.kind}'\` before importing anything else from openai`);if(R)throw new Error(`can't \`import 'openai/shims/${e.kind}'\` after \`import 'openai/shims/${R}'\``);$=t.auto,R=e.kind,N=e.fetch,U=e.Request,W=e.Response,q=e.Headers,j=e.FormData,H=e.Blob,L=e.File,D=e.ReadableStream,V=e.getMultipartRequestOptions,M=e.getDefaultAgent,B=e.fileFromPath,F=e.isFsReadStream}(function({manuallyImported:e}={}){const t=e?"You may need to use polyfills":"Add one of these imports before your first `import … from 'openai'`:\n- `import 'openai/shims/node'` (if you're running on Node)\n- `import 'openai/shims/web'` (otherwise)\n";let r,n,s,i;try{r=fetch,n=Request,s=Response,i=Headers}catch(e){throw new Error(`this environment is missing the following Web Fetch API type: ${e.message}. ${t}`)}return{kind:"web",fetch:r,Request:n,Response:s,Headers:i,FormData:"undefined"!=typeof FormData?FormData:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'FormData' is undefined. ${t}`)}},Blob:"undefined"!=typeof Blob?Blob:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'Blob' is undefined. ${t}`)}},File:"undefined"!=typeof File?File:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'File' is undefined. ${t}`)}},ReadableStream:"undefined"!=typeof ReadableStream?ReadableStream:class{constructor(){throw new Error(`streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${t}`)}},getMultipartRequestOptions:async(e,t)=>({...t,body:new K(e)}),getDefaultAgent:e=>{},fileFromPath:()=>{throw new Error("The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/openai/openai-node#file-uploads")},isFsReadStream:e=>!1}}(),{auto:!0})};z();class J extends Error{}class G extends J{constructor(e,t,r,n){super(`${G.makeMessage(e,t,r)}`),this.status=e,this.headers=n,this.request_id=n?.["x-request-id"],this.error=t;const s=t;this.code=s?.code,this.param=s?.param,this.type=s?.type}static makeMessage(e,t,r){const n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,r,n){if(!e||!n)return new Z({message:r,cause:ze(t)});const s=t?.error;return 400===e?new Y(e,s,r,n):401===e?new ee(e,s,r,n):403===e?new te(e,s,r,n):404===e?new re(e,s,r,n):409===e?new ne(e,s,r,n):422===e?new se(e,s,r,n):429===e?new ie(e,s,r,n):e>=500?new ae(e,s,r,n):new G(e,s,r,n)}}class X extends G{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class Z extends G{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class Q extends Z{constructor({message:e}={}){super({message:e??"Request timed out."})}}class Y extends G{}class ee extends G{}class te extends G{}class re extends G{}class ne extends G{}class se extends G{}class ie extends G{}class ae extends G{}class oe extends J{constructor(){super("Could not parse response content as the length limit was reached")}}class ce extends J{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}var le,ue=function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},he=function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class pe{constructor(){le.set(this,void 0),this.buffer=new Uint8Array,ue(this,le,null,"f")}decode(e){if(null==e)return[];const t=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?(new TextEncoder).encode(e):e;let r=new Uint8Array(this.buffer.length+t.length);r.set(this.buffer),r.set(t,this.buffer.length),this.buffer=r;const n=[];let s;for(;null!=(s=de(this.buffer,he(this,le,"f")));){if(s.carriage&&null==he(this,le,"f")){ue(this,le,s.index,"f");continue}if(null!=he(this,le,"f")&&(s.index!==he(this,le,"f")+1||s.carriage)){n.push(this.decodeText(this.buffer.slice(0,he(this,le,"f")-1))),this.buffer=this.buffer.slice(he(this,le,"f")),ue(this,le,null,"f");continue}const e=null!==he(this,le,"f")?s.preceding-1:s.preceding,t=this.decodeText(this.buffer.slice(0,e));n.push(t),this.buffer=this.buffer.slice(s.index),ue(this,le,null,"f")}return n}decodeText(e){if(null==e)return"";if("string"==typeof e)return e;if("undefined"!=typeof Buffer){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new J(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if("undefined"!=typeof TextDecoder){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return this.textDecoder??(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new J(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new J("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){return this.buffer.length?this.decode("\n"):[]}}function de(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}function fe(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1])return t+2;if(13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return-1}function ge(e){if(e[Symbol.asyncIterator])return e;const t=e.getReader();return{async next(){try{const e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){const e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}le=new WeakMap,pe.NEWLINE_CHARS=new Set(["\n","\r"]),pe.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class me{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;return new me(async function*(){if(r)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let n=!1;try{for await(const r of async function*(e,t){if(!e.body)throw t.abort(),new J("Attempted to iterate over a response with no body");const r=new we,n=new pe,s=ge(e.body);for await(const e of async function*(e){let t=new Uint8Array;for await(const r of e){if(null==r)continue;const e=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?(new TextEncoder).encode(r):r;let n,s=new Uint8Array(t.length+e.length);for(s.set(t),s.set(e,t.length),t=s;-1!==(n=fe(t));)yield t.slice(0,n),t=t.slice(n)}t.length>0&&(yield t)}(s))for(const t of n.decode(e)){const e=r.decode(t);e&&(yield e)}for(const e of n.flush()){const t=r.decode(e);t&&(yield t)}}(e,t))if(!n)if(r.data.startsWith("[DONE]"))n=!0;else if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if(t&&t.error)throw new G(void 0,t.error,void 0,Le(e.headers));yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new G(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}n=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{n||t.abort()}},t)}static fromReadableStream(e,t){let r=!1;return new me(async function*(){if(r)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let n=!1;try{for await(const t of async function*(){const t=new pe,r=ge(e);for await(const e of r)for(const r of t.decode(e))yield r;for(const e of t.flush())yield e}())n||t&&(yield JSON.parse(t));n=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{n||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){const e=[],t=[],r=this.iterator(),n=n=>({next:()=>{if(0===n.length){const n=r.next();e.push(n),t.push(n)}return n.shift()}});return[new me(()=>n(e),this.controller),new me(()=>n(t),this.controller)]}toReadableStream(){const e=this;let t;const r=new TextEncoder;return new D({async start(){t=e[Symbol.asyncIterator]()},async pull(e){try{const{value:n,done:s}=await t.next();if(s)return e.close();const i=r.encode(JSON.stringify(n)+"\n");e.enqueue(i)}catch(t){e.error(t)}},async cancel(){await(t.return?.())}})}}class we{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,n]=function(e){const t=e.indexOf(":");return-1!==t?[e.substring(0,t),":",e.substring(t+1)]:[e,"",""]}(e);return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}const ye=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob,_e=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&Se(e),Se=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer;async function ve(e,t,r){if(e=await e,_e(e))return e;if(ye(e)){const n=await e.blob();t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()??"unknown_file");const s=Se(n)?[await n.arrayBuffer()]:[n];return new L(s,t,r)}const n=await async function(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(Se(e))t.push(await e.arrayBuffer());else{if(!xe(e))throw new Error(`Unexpected data type: ${typeof e}; constructor: ${e?.constructor?.name}; props: ${function(e){return`[${Object.getOwnPropertyNames(e).map(e=>`"${e}"`).join(", ")}]`}(e)}`);for await(const r of e)t.push(r)}return t}(e);if(t||(t=function(e){return be(e.name)||be(e.filename)||be(e.path)?.split(/[\\/]/).pop()}(e)??"unknown_file"),!r?.type){const e=n[0]?.type;"string"==typeof e&&(r={...r,type:e})}return new L(n,t,r)}const be=e=>"string"==typeof e?e:"undefined"!=typeof Buffer&&e instanceof Buffer?String(e):void 0,xe=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],ke=e=>e&&"object"==typeof e&&e.body&&"MultipartBody"===e[Symbol.toStringTag],Ae=async e=>{const t=await Ee(e.body);return V(t,e)},Ee=async e=>{const t=new j;return await Promise.all(Object.entries(e||{}).map(([e,r])=>Ce(t,e,r))),t},Ce=async(e,t,r)=>{if(void 0!==r){if(null==r)throw new TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if((e=>_e(e)||ye(e)||F(e))(r)){const n=await ve(r);e.append(t,n)}else if(Array.isArray(r))await Promise.all(r.map(r=>Ce(e,t+"[]",r)));else{if("object"!=typeof r)throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`);await Promise.all(Object.entries(r).map(([r,n])=>Ce(e,`${t}[${r}]`,n)))}}};var Pe;async function Ie(e){const{response:t}=e;if(e.options.stream)return Ye("response",t.status,t.url,t.headers,t.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(t,e.controller):me.fromSSEResponse(t,e.controller);if(204===t.status)return null;if(e.options.__binaryResponse)return t;const r=t.headers.get("content-type"),n=r?.split(";")[0]?.trim();if(n?.includes("application/json")||n?.endsWith("+json")){const e=await t.json();return Ye("response",t.status,t.url,t.headers,e),Te(e,t)}const s=await t.text();return Ye("response",t.status,t.url,t.headers,s),s}function Te(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}z();class Oe extends Promise{constructor(e,t=Ie){super(e=>{e(null)}),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new Oe(this.responsePromise,async t=>Te(e(await this.parseResponse(t),t),t.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class Re{constructor({baseURL:e,maxRetries:t=2,timeout:r=6e5,httpAgent:n,fetch:s}){this.baseURL=e,this.maxRetries=Ke("maxRetries",t),this.timeout=Ke("timeout",r),this.httpAgent=n,this.fetch=s??N}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...$e(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${et()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(async r=>{const n=r&&Se(r?.body)?new DataView(await r.body.arrayBuffer()):r?.body instanceof DataView?r.body:r?.body instanceof ArrayBuffer?new DataView(r.body):r&&ArrayBuffer.isView(r?.body)?new DataView(r.body.buffer):r?.body;return{method:e,path:t,...r,body:n}}))}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}calculateContentLength(e){if("string"==typeof e){if("undefined"!=typeof Buffer)return Buffer.byteLength(e,"utf8").toString();if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e).length.toString()}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:t=0}={}){const r={...e},{method:n,path:s,query:i,headers:a={}}=r,o=ArrayBuffer.isView(r.body)||r.__binaryRequest&&"string"==typeof r.body?r.body:ke(r.body)?r.body.body:r.body?JSON.stringify(r.body,null,2):null,c=this.calculateContentLength(o),l=this.buildURL(s,i);"timeout"in r&&Ke("timeout",r.timeout),r.timeout=r.timeout??this.timeout;const u=r.httpAgent??this.httpAgent??M(l),h=r.timeout+1e3;return"number"==typeof u?.options?.timeout&&h>(u.options.timeout??0)&&(u.options.timeout=h),this.idempotencyHeader&&"get"!==n&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey),{req:{method:n,...o&&{body:o},headers:this.buildHeaders({options:r,headers:a,contentLength:c,retryCount:t}),...u&&{agent:u},signal:r.signal??null},url:l,timeout:r.timeout}}buildHeaders({options:e,headers:t,contentLength:r,retryCount:n}){const s={};r&&(s["content-length"]=r);const i=this.defaultHeaders(e);return Ze(s,i),Ze(s,t),ke(e.body)&&"node"!==R&&delete s["content-type"],void 0===tt(i,"x-stainless-retry-count")&&void 0===tt(t,"x-stainless-retry-count")&&(s["x-stainless-retry-count"]=String(n)),void 0===tt(i,"x-stainless-timeout")&&void 0===tt(t,"x-stainless-timeout")&&e.timeout&&(s["x-stainless-timeout"]=String(Math.trunc(e.timeout/1e3))),this.validateHeaders(s,t),s}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(e=>[...e])):{...e}:{}}makeStatusError(e,t,r,n){return G.generate(e,t,r,n)}request(e,t=null){return new Oe(this.makeRequest(e,t))}async makeRequest(e,t){const r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);const{req:s,url:i,timeout:a}=this.buildRequest(r,{retryCount:n-t});if(await this.prepareRequest(s,{url:i,options:r}),Ye("request",i,r,s.headers),r.signal?.aborted)throw new X;const o=new AbortController,c=await this.fetchWithTimeout(i,s,a,o).catch(ze);if(c instanceof Error){if(r.signal?.aborted)throw new X;if(t)return this.retryRequest(r,t);if("AbortError"===c.name)throw new Q;throw new Z({cause:c})}const l=Le(c.headers);if(!c.ok){if(t&&this.shouldRetry(c))return Ye(`response (error; retrying, ${t} attempts remaining)`,c.status,i,l),this.retryRequest(r,t,l);const e=await c.text().catch(e=>ze(e).message),n=Ue(e),s=n?void 0:e;throw Ye(`response (error; ${t?"(error; no more retries left)":"(error; not retryable)"})`,c.status,i,l,s),this.makeStatusError(c.status,n,s,l)}return{response:c,options:r,controller:o}}requestAPIList(e,t){const r=this.makeRequest(t,null);return new je(this,r,e)}buildURL(e,t){const r=qe(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return Ge(n)||(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new J(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,t,r,n){const{signal:s,...i}=t||{};s&&s.addEventListener("abort",()=>n.abort());const a=setTimeout(()=>n.abort(),r),o={signal:n.signal,...i};return o.method&&(o.method=o.method.toUpperCase()),this.fetch.call(void 0,e,o).finally(()=>{clearTimeout(a)})}shouldRetry(e){const t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||e.status>=500)}async retryRequest(e,t,r){let n;const s=r?.["retry-after-ms"];if(s){const e=parseFloat(s);Number.isNaN(e)||(n=e)}const i=r?.["retry-after"];if(i&&!n){const e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){const r=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,r)}return await He(n),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){const r=t-e;return Math.min(.5*Math.pow(2,r),8)*(1-.25*Math.random())*1e3}getUserAgent(){return`${this.constructor.name}/JS ${O}`}}class Ne{constructor(e,t,r,n){Pe.set(this,void 0),function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");"a"===n?s.call(e,r):s?s.value=r:t.set(e,r)}(this,Pe,e,"f"),this.options=n,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageInfo()}async getNextPage(){const e=this.nextPageInfo();if(!e)throw new J("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");const t={...this.options};if("params"in e&&"object"==typeof t.query)t.query={...t.query,...e.params};else if("url"in e){const r=[...Object.entries(t.query||{}),...e.url.searchParams.entries()];for(const[t,n]of r)e.url.searchParams.set(t,n);t.query=void 0,t.path=e.url.toString()}return await function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}(this,Pe,"f").requestAPIList(this.constructor,t)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(Pe=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const t of e.getPaginatedItems())yield t}}class je extends Oe{constructor(e,t,r){super(t,async t=>new r(e,t.response,await Ie(t),t.options))}async*[Symbol.asyncIterator](){const e=await(this);for await(const t of e)yield t}}const Le=e=>new Proxy(Object.fromEntries(e.entries()),{get(e,t){const r=t.toString();return e[r.toLowerCase()]||e[r]}}),De={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__metadata:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},Ve=e=>"object"==typeof e&&null!==e&&!Ge(e)&&Object.keys(e).every(e=>Xe(De,e)),Me=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",Be=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown";let Fe;const $e=()=>Fe??(Fe=(()=>{if("undefined"!=typeof Deno&&null!=Deno.build)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":O,"X-Stainless-OS":Be(Deno.build.os),"X-Stainless-Arch":Me(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":O,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if("[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0))return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":O,"X-Stainless-OS":Be(process.platform),"X-Stainless-Arch":Me(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};const e=function(){if("undefined"==typeof navigator||!navigator)return null;const e=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:t,pattern:r}of e){const e=r.exec(navigator.userAgent);if(e)return{browser:t,version:`${e[1]||0}.${e[2]||0}.${e[3]||0}`}}return null}();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":O,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":O,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),Ue=e=>{try{return JSON.parse(e)}catch(e){return}},We=/^[a-z][a-z0-9+.-]*:/i,qe=e=>We.test(e),He=e=>new Promise(t=>setTimeout(t,e)),Ke=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new J(`${e} must be an integer`);if(t<0)throw new J(`${e} must be a positive integer`);return t},ze=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e)try{return new Error(JSON.stringify(e))}catch{}return new Error(e)},Je=e=>"undefined"!=typeof process?process.env?.[e]?.trim()??void 0:"undefined"!=typeof Deno?Deno.env?.get?.(e)?.trim():void 0;function Ge(e){if(!e)return!0;for(const t in e)return!1;return!0}function Xe(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Ze(e,t){for(const r in t){if(!Xe(t,r))continue;const n=r.toLowerCase();if(!n)continue;const s=t[r];null===s?delete e[n]:void 0!==s&&(e[n]=s)}}const Qe=new Set(["authorization","api-key"]);function Ye(e,...t){if("undefined"!=typeof process&&"true"===process?.env?.DEBUG){const r=t.map(e=>{if(!e)return e;if(e.headers){const t={...e,headers:{...e.headers}};for(const r in e.headers)Qe.has(r.toLowerCase())&&(t.headers[r]="REDACTED");return t}let t=null;for(const r in e)Qe.has(r.toLowerCase())&&(t??(t={...e}),t[r]="REDACTED");return t??e});console.log(`OpenAI:DEBUG:${e}`,...r)}}const et=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),tt=(e,t)=>{const r=t.toLowerCase();if((e=>"function"==typeof e?.get)(e)){const n=t[0]?.toUpperCase()+t.substring(1).replace(/([^\w])(\w)/g,(e,t,r)=>t+r.toUpperCase());for(const s of[t,r,t.toUpperCase(),n]){const t=e.get(s);if(t)return t}}for(const[n,s]of Object.entries(e))if(n.toLowerCase()===r)return Array.isArray(s)?(s.length<=1||console.warn(`Received ${s.length} entries for the ${t} header, using the first entry.`),s[0]):s};function rt(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}class nt{constructor(e){this._client=e}}class st extends nt{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class it extends nt{list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/chat/completions/${e}/messages`,ut,{query:t,...r})}}class at extends Ne{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageParams(){return null}nextPageInfo(){return null}}class ot extends Ne{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageParams(){const e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;const t=Object.fromEntries(e.url.searchParams);return Object.keys(t).length?t:null}nextPageInfo(){const e=this.getPaginatedItems();if(!e.length)return null;const t=e[e.length-1]?.id;return t?{params:{after:t}}:null}}class ct extends nt{constructor(){super(...arguments),this.messages=new it(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(`/chat/completions/${e}`,t)}update(e,t,r){return this._client.post(`/chat/completions/${e}`,{body:t,...r})}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/chat/completions",lt,{query:e,...t})}del(e,t){return this._client.delete(`/chat/completions/${e}`,t)}}class lt extends ot{}class ut extends ot{}ct.ChatCompletionsPage=lt,ct.Messages=it;class ht extends nt{constructor(){super(...arguments),this.completions=new ct(this._client)}}ht.Completions=ct,ht.ChatCompletionsPage=lt;class pt extends nt{create(e,t){const r=!!e.encoding_format;let n=r?e.encoding_format:"base64";r&&Ye("Request","User defined encoding_format:",e.encoding_format);const s=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return r?s:(Ye("response","Decoding base64 embeddings to float32 array"),s._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{const t=e.embedding;e.embedding=(e=>{if("undefined"!=typeof Buffer){const t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{const t=atob(e),r=t.length,n=new Uint8Array(r);for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}})(t)}),e)))}}class dt extends nt{create(e,t){return this._client.post("/files",Ae({body:e,...t}))}retrieve(e,t){return this._client.get(`/files/${e}`,t)}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/files",ft,{query:e,...t})}del(e,t){return this._client.delete(`/files/${e}`,t)}content(e,t){return this._client.get(`/files/${e}/content`,{...t,headers:{Accept:"application/binary",...t?.headers},__binaryResponse:!0})}retrieveContent(e,t){return this._client.get(`/files/${e}/content`,t)}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=18e5}={}){const n=new Set(["processed","error","deleted"]),s=Date.now();let i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await He(t),i=await this.retrieve(e),Date.now()-s>r)throw new Q({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return i}}class ft extends ot{}dt.FileObjectsPage=ft;class gt extends nt{createVariation(e,t){return this._client.post("/images/variations",Ae({body:e,...t}))}edit(e,t){return this._client.post("/images/edits",Ae({body:e,...t}))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class mt extends nt{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:{Accept:"application/octet-stream",...t?.headers},__binaryResponse:!0})}}class wt extends nt{create(e,t){return this._client.post("/audio/transcriptions",Ae({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}}))}}class yt extends nt{create(e,t){return this._client.post("/audio/translations",Ae({body:e,...t,__metadata:{model:e.model}}))}}class _t extends nt{constructor(){super(...arguments),this.transcriptions=new wt(this._client),this.translations=new yt(this._client),this.speech=new mt(this._client)}}_t.Transcriptions=wt,_t.Translations=yt,_t.Speech=mt;class St extends nt{create(e,t){return this._client.post("/moderations",{body:e,...t})}}class vt extends nt{retrieve(e,t){return this._client.get(`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",bt,e)}del(e,t){return this._client.delete(`/models/${e}`,t)}}class bt extends at{}vt.ModelsPage=bt;class xt extends nt{}class kt extends nt{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class At extends nt{constructor(){super(...arguments),this.graders=new kt(this._client)}}At.Graders=kt;class Et extends nt{create(e,t,r){return this._client.getAPIList(`/fine_tuning/checkpoints/${e}/permissions`,Ct,{body:t,method:"post",...r})}retrieve(e,t={},r){return Ve(t)?this.retrieve(e,{},t):this._client.get(`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...r})}del(e,t,r){return this._client.delete(`/fine_tuning/checkpoints/${e}/permissions/${t}`,r)}}class Ct extends at{}Et.PermissionCreateResponsesPage=Ct;class Pt extends nt{constructor(){super(...arguments),this.permissions=new Et(this._client)}}Pt.Permissions=Et,Pt.PermissionCreateResponsesPage=Ct;class It extends nt{list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,Tt,{query:t,...r})}}class Tt extends ot{}It.FineTuningJobCheckpointsPage=Tt;class Ot extends nt{constructor(){super(...arguments),this.checkpoints=new It(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(`/fine_tuning/jobs/${e}`,t)}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",Rt,{query:e,...t})}cancel(e,t){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return Ve(t)?this.listEvents(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,Nt,{query:t,...r})}pause(e,t){return this._client.post(`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(`/fine_tuning/jobs/${e}/resume`,t)}}class Rt extends ot{}class Nt extends ot{}Ot.FineTuningJobsPage=Rt,Ot.FineTuningJobEventsPage=Nt,Ot.Checkpoints=It,Ot.FineTuningJobCheckpointsPage=Tt;class jt extends nt{constructor(){super(...arguments),this.methods=new xt(this._client),this.jobs=new Ot(this._client),this.checkpoints=new Pt(this._client),this.alpha=new At(this._client)}}jt.Methods=xt,jt.Jobs=Ot,jt.FineTuningJobsPage=Rt,jt.FineTuningJobEventsPage=Nt,jt.Checkpoints=Pt,jt.Alpha=At;class Lt extends nt{}class Dt extends nt{constructor(){super(...arguments),this.graderModels=new Lt(this._client)}}Dt.GraderModels=Lt;class Vt extends nt{create(e,t,r){return this._client.post(`/vector_stores/${e}/files`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/vector_stores/${e}/files/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/vector_stores/${e}/files`,Mt,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t,r){return this._client.delete(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){const n=await this.create(e,t,r);return await this.poll(e,n.id,r)}async poll(e,t,r){const n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){const s=await this.retrieve(e,t,{...r,headers:n}).withResponse(),i=s.data;switch(i.status){case"in_progress":let e=5e3;if(r?.pollIntervalMs)e=r.pollIntervalMs;else{const t=s.response.headers.get("openai-poll-after-ms");if(t){const r=parseInt(t);isNaN(r)||(e=r)}}await He(e);break;case"failed":case"completed":return i}}}async upload(e,t,r){const n=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:n.id},r)}async uploadAndPoll(e,t,r){const n=await this.upload(e,t,r);return await this.poll(e,n.id,r)}content(e,t,r){return this._client.getAPIList(`/vector_stores/${e}/files/${t}/content`,Bt,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class Mt extends ot{}class Bt extends at{}Vt.VectorStoreFilesPage=Mt,Vt.FileContentResponsesPage=Bt;class Ft extends nt{create(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/file_batches/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}cancel(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){const n=await this.create(e,t);return await this.poll(e,n.id,r)}listFiles(e,t,r={},n){return Ve(r)?this.listFiles(e,t,{},r):this._client.getAPIList(`/vector_stores/${e}/file_batches/${t}/files`,Mt,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}async poll(e,t,r){const n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){const{data:s,response:i}=await this.retrieve(e,t,{...r,headers:n}).withResponse();switch(s.status){case"in_progress":let e=5e3;if(r?.pollIntervalMs)e=r.pollIntervalMs;else{const t=i.headers.get("openai-poll-after-ms");if(t){const r=parseInt(t);isNaN(r)||(e=r)}}await He(e);break;case"failed":case"cancelled":case"completed":return s}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},n){if(null==t||0==t.length)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const s=n?.maxConcurrency??5,i=Math.min(s,t.length),a=this._client,o=t.values(),c=[...r],l=Array(i).fill(o).map(async function(e){for(let t of e){const e=await a.files.create({file:t,purpose:"assistants"},n);c.push(e.id)}});return await(async e=>{const t=await Promise.allSettled(e),r=t.filter(e=>"rejected"===e.status);if(r.length){for(const e of r)console.error(e.reason);throw new Error(`${r.length} promise(s) failed - see the above errors`)}const n=[];for(const e of t)"fulfilled"===e.status&&n.push(e.value);return n})(l),await this.createAndPoll(e,{file_ids:c})}}class $t extends nt{constructor(){super(...arguments),this.files=new Vt(this._client),this.fileBatches=new Ft(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/vector_stores/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/vector_stores",Ut,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}del(e,t){return this._client.delete(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}search(e,t,r){return this._client.getAPIList(`/vector_stores/${e}/search`,Wt,{body:t,method:"post",...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class Ut extends ot{}class Wt extends at{}$t.VectorStoresPage=Ut,$t.VectorStoreSearchResponsesPage=Wt,$t.Files=Vt,$t.VectorStoreFilesPage=Mt,$t.FileContentResponsesPage=Bt,$t.FileBatches=Ft;class qt extends nt{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/assistants/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/assistants",Ht,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}del(e,t){return this._client.delete(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class Ht extends ot{}function Kt(e){return"function"==typeof e.parse}qt.AssistantsPage=Ht;const zt=e=>"assistant"===e?.role,Jt=e=>"function"===e?.role,Gt=e=>"tool"===e?.role;var Xt,Zt,Qt,Yt,er,tr,rr,nr,sr,ir,ar,or,cr,lr=function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},ur=function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class hr{constructor(){Xt.add(this),this.controller=new AbortController,Zt.set(this,void 0),Qt.set(this,()=>{}),Yt.set(this,()=>{}),er.set(this,void 0),tr.set(this,()=>{}),rr.set(this,()=>{}),nr.set(this,{}),sr.set(this,!1),ir.set(this,!1),ar.set(this,!1),or.set(this,!1),lr(this,Zt,new Promise((e,t)=>{lr(this,Qt,e,"f"),lr(this,Yt,t,"f")}),"f"),lr(this,er,new Promise((e,t)=>{lr(this,tr,e,"f"),lr(this,rr,t,"f")}),"f"),ur(this,Zt,"f").catch(()=>{}),ur(this,er,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},ur(this,Xt,"m",cr).bind(this))},0)}_connected(){this.ended||(ur(this,Qt,"f").call(this),this._emit("connect"))}get ended(){return ur(this,sr,"f")}get errored(){return ur(this,ir,"f")}get aborted(){return ur(this,ar,"f")}abort(){this.controller.abort()}on(e,t){return(ur(this,nr,"f")[e]||(ur(this,nr,"f")[e]=[])).push({listener:t}),this}off(e,t){const r=ur(this,nr,"f")[e];if(!r)return this;const n=r.findIndex(e=>e.listener===t);return n>=0&&r.splice(n,1),this}once(e,t){return(ur(this,nr,"f")[e]||(ur(this,nr,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{lr(this,or,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){lr(this,or,!0,"f"),await ur(this,er,"f")}_emit(e,...t){if(ur(this,sr,"f"))return;"end"===e&&(lr(this,sr,!0,"f"),ur(this,tr,"f").call(this));const r=ur(this,nr,"f")[e];if(r&&(ur(this,nr,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){const e=t[0];return ur(this,or,"f")||r?.length||Promise.reject(e),ur(this,Yt,"f").call(this,e),ur(this,rr,"f").call(this,e),void this._emit("end")}if("error"===e){const e=t[0];ur(this,or,"f")||r?.length||Promise.reject(e),ur(this,Yt,"f").call(this,e),ur(this,rr,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function pr(e){return"auto-parseable-response-format"===e?.$brand}function dr(e){return"auto-parseable-tool"===e?.$brand}function fr(e,t){const r=e.choices.map(e=>{if("length"===e.finish_reason)throw new oe;if("content_filter"===e.finish_reason)throw new ce;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>function(e,t){const r=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:dr(r)?r.$parseRaw(t.function.arguments):r?.function.strict?JSON.parse(t.function.arguments):null}}}(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?gr(t,e.message.content):null}}});return{...e,choices:r}}function gr(e,t){return"json_schema"!==e.response_format?.type?null:"json_schema"===e.response_format?.type?"$parseRaw"in e.response_format?e.response_format.$parseRaw(t):JSON.parse(t):null}function mr(e,t){if(!e)return!1;const r=e.tools?.find(e=>e.function?.name===t.function.name);return dr(r)||r?.function.strict||!1}function wr(e){return!!pr(e.response_format)||(e.tools?.some(e=>dr(e)||"function"===e.type&&!0===e.function.strict)??!1)}Zt=new WeakMap,Qt=new WeakMap,Yt=new WeakMap,er=new WeakMap,tr=new WeakMap,rr=new WeakMap,nr=new WeakMap,sr=new WeakMap,ir=new WeakMap,ar=new WeakMap,or=new WeakMap,Xt=new WeakSet,cr=function(e){if(lr(this,ir,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new X),e instanceof X)return lr(this,ar,!0,"f"),this._emit("abort",e);if(e instanceof J)return this._emit("error",e);if(e instanceof Error){const t=new J(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new J(String(e)))};var yr,_r,Sr,vr,br,xr,kr,Ar,Er=function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};const Cr=10;class Pr extends hr{constructor(){super(...arguments),yr.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);const t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t)if(this._emit("message",e),(Jt(e)||Gt(e))&&e.content)this._emit("functionCallResult",e.content);else if(zt(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(zt(e)&&e.tool_calls)for(const t of e.tool_calls)"function"===t.type&&this._emit("functionCall",t.function)}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new J("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),Er(this,yr,"m",_r).call(this)}async finalMessage(){return await this.done(),Er(this,yr,"m",Sr).call(this)}async finalFunctionCall(){return await this.done(),Er(this,yr,"m",vr).call(this)}async finalFunctionCallResult(){return await this.done(),Er(this,yr,"m",br).call(this)}async totalUsage(){return await this.done(),Er(this,yr,"m",xr).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const t=Er(this,yr,"m",Sr).call(this);t&&this._emit("finalMessage",t);const r=Er(this,yr,"m",_r).call(this);r&&this._emit("finalContent",r);const n=Er(this,yr,"m",vr).call(this);n&&this._emit("finalFunctionCall",n);const s=Er(this,yr,"m",br).call(this);null!=s&&this._emit("finalFunctionCallResult",s),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",Er(this,yr,"m",xr).call(this))}async _createChatCompletion(e,t,r){const n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),Er(this,yr,"m",kr).call(this,t);const s=await e.chat.completions.create({...t,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(fr(s,t))}async _runChatCompletion(e,t,r){for(const e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,r)}async _runFunctions(e,t,r){const n="function",{function_call:s="auto",stream:i,...a}=t,o="string"!=typeof s&&s?.name,{maxChatCompletions:c=Cr}=r||{},l={};for(const e of t.functions)l[e.name||e.function.name]=e;const u=t.functions.map(e=>({name:e.name||e.function.name,parameters:e.parameters,description:e.description}));for(const e of t.messages)this._addMessage(e,!1);for(let t=0;t<c;++t){const t=await this._createChatCompletion(e,{...a,function_call:s,functions:u,messages:[...this.messages]},r),i=t.choices[0]?.message;if(!i)throw new J("missing message in ChatCompletion response");if(!i.function_call)return;const{name:c,arguments:h}=i.function_call,p=l[c];if(!p){const e=`Invalid function_call: ${JSON.stringify(c)}. Available options are: ${u.map(e=>JSON.stringify(e.name)).join(", ")}. Please try again`;this._addMessage({role:n,name:c,content:e});continue}if(o&&o!==c){const e=`Invalid function_call: ${JSON.stringify(c)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,name:c,content:e});continue}let d;try{d=Kt(p)?await p.parse(h):h}catch(e){this._addMessage({role:n,name:c,content:e instanceof Error?e.message:String(e)});continue}const f=await p.function(d,this),g=Er(this,yr,"m",Ar).call(this,f);if(this._addMessage({role:n,name:c,content:g}),o)return}}async _runTools(e,t,r){const n="tool",{tool_choice:s="auto",stream:i,...a}=t,o="string"!=typeof s&&s?.function?.name,{maxChatCompletions:c=Cr}=r||{},l=t.tools.map(e=>{if(dr(e)){if(!e.$callback)throw new J("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(const e of l)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);const h="tools"in t?l.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(const e of t.messages)this._addMessage(e,!1);for(let t=0;t<c;++t){const t=await this._createChatCompletion(e,{...a,tool_choice:s,tools:h,messages:[...this.messages]},r),i=t.choices[0]?.message;if(!i)throw new J("missing message in ChatCompletion response");if(!i.tool_calls?.length)return;for(const e of i.tool_calls){if("function"!==e.type)continue;const t=e.id,{name:r,arguments:s}=e.function,i=u[r];if(!i){const e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:t,content:e});continue}if(o&&o!==r){const e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:t,content:e});continue}let a;try{a=Kt(i)?await i.parse(s):s}catch(e){const r=e instanceof Error?e.message:String(e);this._addMessage({role:n,tool_call_id:t,content:r});continue}const c=await i.function(a,this),l=Er(this,yr,"m",Ar).call(this,c);if(this._addMessage({role:n,tool_call_id:t,content:l}),o)return}}}}yr=new WeakSet,_r=function(){return Er(this,yr,"m",Sr).call(this).content??null},Sr=function(){let e=this.messages.length;for(;e-- >0;){const t=this.messages[e];if(zt(t)){const{function_call:e,...r}=t,n={...r,content:t.content??null,refusal:t.refusal??null};return e&&(n.function_call=e),n}}throw new J("stream ended without producing a ChatCompletionMessage with role=assistant")},vr=function(){for(let e=this.messages.length-1;e>=0;e--){const t=this.messages[e];if(zt(t)&&t?.function_call)return t.function_call;if(zt(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},br=function(){for(let e=this.messages.length-1;e>=0;e--){const t=this.messages[e];if(Jt(t)&&null!=t.content)return t.content;if(Gt(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},xr=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},kr=function(e){if(null!=e.n&&e.n>1)throw new J("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},Ar=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class Ir extends Pr{static runFunctions(e,t,r){const n=new Ir,s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,s)),n}static runTools(e,t,r){const n=new Ir,s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,s)),n}_addMessage(e,t=!0){super._addMessage(e,t),zt(e)&&e.content&&this._emit("content",e.content)}}class Tr extends Error{}class Or extends Error{}const Rr=e=>function(e,t=511){if("string"!=typeof e)throw new TypeError("expecting str, got "+typeof e);if(!e.trim())throw new Error(`${e} is empty`);return((e,t)=>{const r=e.length;let n=0;const s=e=>{throw new Tr(`${e} at position ${n}`)},i=e=>{throw new Or(`${e} at position ${n}`)},a=()=>(h(),n>=r&&s("Unexpected end of input"),'"'===e[n]?o():"{"===e[n]?c():"["===e[n]?l():"null"===e.substring(n,n+4)||16&t&&r-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||32&t&&r-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||32&t&&r-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||128&t&&r-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||256&t&&1<r-n&&r-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||64&t&&r-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u()),o=()=>{const a=n;let o=!1;for(n++;n<r&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(a,++n-Number(o)))}catch(e){i(String(e))}else if(1&t)try{return JSON.parse(e.substring(a,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},c=()=>{n++,h();const i={};try{for(;"}"!==e[n];){if(h(),n>=r&&8&t)return i;const s=o();h(),n++;try{const e=a();Object.defineProperty(i,s,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(8&t)return i;throw e}h(),","===e[n]&&n++}}catch(e){if(8&t)return i;s("Expected '}' at end of object")}return n++,i},l=()=>{n++;const r=[];try{for(;"]"!==e[n];)r.push(a()),h(),","===e[n]&&n++}catch(e){if(4&t)return r;s("Expected ']' at end of array")}return n++,r},u=()=>{if(0===n){"-"===e&&2&t&&s("Not sure what '-' is");try{return JSON.parse(e)}catch(r){if(2&t)try{return"."===e[e.length-1]?JSON.parse(e.substring(0,e.lastIndexOf("."))):JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(r))}}const a=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=r||2&t||s("Unterminated number literal");try{return JSON.parse(e.substring(a,n))}catch(r){"-"===e.substring(a,n)&&2&t&&s("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;n<r&&" \n\r\t".includes(e[n]);)n++};return a()})(e.trim(),t)}(e,509);var Nr,jr,Lr,Dr,Vr,Mr,Br,Fr,$r,Ur,Wr,qr,Hr=function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},Kr=function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class zr extends Pr{constructor(e){super(),Nr.add(this),jr.set(this,void 0),Lr.set(this,void 0),Dr.set(this,void 0),Hr(this,jr,e,"f"),Hr(this,Lr,[],"f")}get currentChatCompletionSnapshot(){return Kr(this,Dr,"f")}static fromReadableStream(e){const t=new zr(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){const n=new zr(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,r){super._createChatCompletion;const n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),Kr(this,Nr,"m",Vr).call(this);const s=await e.chat.completions.create({...t,stream:!0},{...r,signal:this.controller.signal});this._connected();for await(const e of s)Kr(this,Nr,"m",Br).call(this,e);if(s.controller.signal?.aborted)throw new X;return this._addChatCompletion(Kr(this,Nr,"m",Ur).call(this))}async _fromReadableStream(e,t){const r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),Kr(this,Nr,"m",Vr).call(this),this._connected();const n=me.fromReadableStream(e,this.controller);let s;for await(const e of n)s&&s!==e.id&&this._addChatCompletion(Kr(this,Nr,"m",Ur).call(this)),Kr(this,Nr,"m",Br).call(this,e),s=e.id;if(n.controller.signal?.aborted)throw new X;return this._addChatCompletion(Kr(this,Nr,"m",Ur).call(this))}[(jr=new WeakMap,Lr=new WeakMap,Dr=new WeakMap,Nr=new WeakSet,Vr=function(){this.ended||Hr(this,Dr,void 0,"f")},Mr=function(e){let t=Kr(this,Lr,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},Kr(this,Lr,"f")[e.index]=t,t)},Br=function(e){if(this.ended)return;const t=Kr(this,Nr,"m",qr).call(this,e);this._emit("chunk",e,t);for(const r of e.choices){const e=t.choices[r.index];null!=r.delta.content&&"assistant"===e.message?.role&&e.message?.content&&(this._emit("content",r.delta.content,e.message.content),this._emit("content.delta",{delta:r.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=r.delta.refusal&&"assistant"===e.message?.role&&e.message?.refusal&&this._emit("refusal.delta",{delta:r.delta.refusal,snapshot:e.message.refusal}),null!=r.logprobs?.content&&"assistant"===e.message?.role&&this._emit("logprobs.content.delta",{content:r.logprobs?.content,snapshot:e.logprobs?.content??[]}),null!=r.logprobs?.refusal&&"assistant"===e.message?.role&&this._emit("logprobs.refusal.delta",{refusal:r.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});const n=Kr(this,Nr,"m",Mr).call(this,e);e.finish_reason&&(Kr(this,Nr,"m",$r).call(this,e),null!=n.current_tool_call_index&&Kr(this,Nr,"m",Fr).call(this,e,n.current_tool_call_index));for(const t of r.delta.tool_calls??[])n.current_tool_call_index!==t.index&&(Kr(this,Nr,"m",$r).call(this,e),null!=n.current_tool_call_index&&Kr(this,Nr,"m",Fr).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(const t of r.delta.tool_calls??[]){const r=e.message.tool_calls?.[t.index];r?.type&&("function"===r?.type?this._emit("tool_calls.function.arguments.delta",{name:r.function?.name,index:t.index,arguments:r.function.arguments,parsed_arguments:r.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):Xr())}}},Fr=function(e,t){if(Kr(this,Nr,"m",Mr).call(this,e).done_tool_calls.has(t))return;const r=e.message.tool_calls?.[t];if(!r)throw new Error("no tool call snapshot");if(!r.type)throw new Error("tool call snapshot missing `type`");if("function"===r.type){const e=Kr(this,jr,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===r.function.name);this._emit("tool_calls.function.arguments.done",{name:r.function.name,index:t,arguments:r.function.arguments,parsed_arguments:dr(e)?e.$parseRaw(r.function.arguments):e?.function.strict?JSON.parse(r.function.arguments):null})}else r.type},$r=function(e){const t=Kr(this,Nr,"m",Mr).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;const r=Kr(this,Nr,"m",Wr).call(this);this._emit("content.done",{content:e.message.content,parsed:r?r.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},Ur=function(){if(this.ended)throw new J("stream has ended, this shouldn't happen");const e=Kr(this,Dr,"f");if(!e)throw new J("request ended without sending any chunks");return Hr(this,Dr,void 0,"f"),Hr(this,Lr,[],"f"),function(e,t){const{id:r,choices:n,created:s,model:i,system_fingerprint:a,...o}=e,c={...o,id:r,choices:n.map(({message:t,finish_reason:r,index:n,logprobs:s,...i})=>{if(!r)throw new J(`missing finish_reason for choice ${n}`);const{content:a=null,function_call:o,tool_calls:c,...l}=t,u=t.role;if(!u)throw new J(`missing role for choice ${n}`);if(o){const{arguments:e,name:c}=o;if(null==e)throw new J(`missing function_call.arguments for choice ${n}`);if(!c)throw new J(`missing function_call.name for choice ${n}`);return{...i,message:{content:a,function_call:{arguments:e,name:c},role:u,refusal:t.refusal??null},finish_reason:r,index:n,logprobs:s}}return c?{...i,index:n,finish_reason:r,logprobs:s,message:{...l,role:u,content:a,refusal:t.refusal??null,tool_calls:c.map((t,r)=>{const{function:s,type:i,id:a,...o}=t,{arguments:c,name:l,...u}=s||{};if(null==a)throw new J(`missing choices[${n}].tool_calls[${r}].id\n${Jr(e)}`);if(null==i)throw new J(`missing choices[${n}].tool_calls[${r}].type\n${Jr(e)}`);if(null==l)throw new J(`missing choices[${n}].tool_calls[${r}].function.name\n${Jr(e)}`);if(null==c)throw new J(`missing choices[${n}].tool_calls[${r}].function.arguments\n${Jr(e)}`);return{...o,id:a,type:i,function:{...u,name:l,arguments:c}}})}}:{...i,message:{...l,content:a,role:u,refusal:t.refusal??null},finish_reason:r,index:n,logprobs:s}}),created:s,model:i,object:"chat.completion",...a?{system_fingerprint:a}:{}};return function(e,t){return t&&wr(t)?fr(e,t):{...e,choices:e.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(c,t)}(e,Kr(this,jr,"f"))},Wr=function(){const e=Kr(this,jr,"f")?.response_format;return pr(e)?e:null},qr=function(e){var t,r,n,s;let i=Kr(this,Dr,"f");const{choices:a,...o}=e;i?Object.assign(i,o):i=Hr(this,Dr,{...o,choices:[]},"f");for(const{delta:a,finish_reason:o,index:c,logprobs:l=null,...u}of e.choices){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:o,index:c,message:{},logprobs:l,...u}),l)if(e.logprobs){const{content:n,refusal:s,...i}=l;Gr(),Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),s&&((r=e.logprobs).refusal??(r.refusal=[]),e.logprobs.refusal.push(...s))}else e.logprobs=Object.assign({},l);if(o&&(e.finish_reason=o,Kr(this,jr,"f")&&wr(Kr(this,jr,"f")))){if("length"===o)throw new oe;if("content_filter"===o)throw new ce}if(Object.assign(e,u),!a)continue;const{content:h,refusal:p,function_call:d,role:f,tool_calls:g,...m}=a;if(Gr(),Object.assign(e.message,m),p&&(e.message.refusal=(e.message.refusal||"")+p),f&&(e.message.role=f),d&&(e.message.function_call?(d.name&&(e.message.function_call.name=d.name),d.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=d.arguments)):e.message.function_call=d),h&&(e.message.content=(e.message.content||"")+h,!e.message.refusal&&Kr(this,Nr,"m",Wr).call(this)&&(e.message.parsed=Rr(e.message.content))),g){e.message.tool_calls||(e.message.tool_calls=[]);for(const{index:t,id:r,type:n,function:i,...a}of g){const o=(s=e.message.tool_calls)[t]??(s[t]={});Object.assign(o,a),r&&(o.id=r),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,mr(Kr(this,jr,"f"),o)&&(o.function.parsed_arguments=Rr(o.function.arguments)))}}}return i},Symbol.asyncIterator)](){const e=[],t=[];let r=!1;return this.on("chunk",r=>{const n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{r=!0;for(const e of t)e.resolve(void 0);t.length=0}),this.on("abort",e=>{r=!0;for(const r of t)r.reject(e);t.length=0}),this.on("error",e=>{r=!0;for(const r of t)r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new me(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function Jr(e){return JSON.stringify(e)}function Gr(e){}function Xr(e){}class Zr extends zr{static fromReadableStream(e){const t=new Zr(null);return t._run(()=>t._fromReadableStream(e)),t}static runFunctions(e,t,r){const n=new Zr(null),s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,s)),n}static runTools(e,t,r){const n=new Zr(t),s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,s)),n}}class Qr extends nt{parse(e,t){return function(e){for(const t of e??[]){if("function"!==t.type)throw new J(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new J(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}(e.tools),this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap(t=>fr(t,e))}runFunctions(e,t){return e.stream?Zr.runFunctions(this._client,e,t):Ir.runFunctions(this._client,e,t)}runTools(e,t){return e.stream?Zr.runTools(this._client,e,t):Ir.runTools(this._client,e,t)}stream(e,t){return zr.createChatCompletion(this._client,e,t)}}class Yr extends nt{constructor(){super(...arguments),this.completions=new Qr(this._client)}}!function(e){e.Completions=Qr}(Yr||(Yr={}));class en extends nt{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class tn extends nt{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class rn extends nt{constructor(){super(...arguments),this.sessions=new en(this._client),this.transcriptionSessions=new tn(this._client)}}rn.Sessions=en,rn.TranscriptionSessions=tn;var nn,sn,an,on,cn,ln,un,hn,pn,dn,fn,gn,mn,wn,yn,_n,Sn,vn,bn,xn,kn,An,En=function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},Cn=function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r};class Pn extends hr{constructor(){super(...arguments),nn.add(this),sn.set(this,[]),an.set(this,{}),on.set(this,{}),cn.set(this,void 0),ln.set(this,void 0),un.set(this,void 0),hn.set(this,void 0),pn.set(this,void 0),dn.set(this,void 0),fn.set(this,void 0),gn.set(this,void 0),mn.set(this,void 0)}[(sn=new WeakMap,an=new WeakMap,on=new WeakMap,cn=new WeakMap,ln=new WeakMap,un=new WeakMap,hn=new WeakMap,pn=new WeakMap,dn=new WeakMap,fn=new WeakMap,gn=new WeakMap,mn=new WeakMap,nn=new WeakSet,Symbol.asyncIterator)](){const e=[],t=[];let r=!1;return this.on("event",r=>{const n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{r=!0;for(const e of t)e.resolve(void 0);t.length=0}),this.on("abort",e=>{r=!0;for(const r of t)r.reject(e);t.length=0}),this.on("error",e=>{r=!0;for(const r of t)r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const t=new Pn;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){const r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();const n=me.fromReadableStream(e,this.controller);for await(const e of n)En(this,nn,"m",wn).call(this,e);if(n.controller.signal?.aborted)throw new X;return this._addRun(En(this,nn,"m",yn).call(this))}toReadableStream(){return new me(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,n,s){const i=new Pn;return i._run(()=>i._runToolAssistantStream(e,t,r,n,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),i}async _createToolAssistantStream(e,t,r,n,s){const i=s?.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));const a={...n,stream:!0},o=await e.submitToolOutputs(t,r,a,{...s,signal:this.controller.signal});this._connected();for await(const e of o)En(this,nn,"m",wn).call(this,e);if(o.controller.signal?.aborted)throw new X;return this._addRun(En(this,nn,"m",yn).call(this))}static createThreadAssistantStream(e,t,r){const n=new Pn;return n._run(()=>n._threadAssistantStream(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,r,n){const s=new Pn;return s._run(()=>s._runAssistantStream(e,t,r,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),s}currentEvent(){return En(this,fn,"f")}currentRun(){return En(this,gn,"f")}currentMessageSnapshot(){return En(this,cn,"f")}currentRunStepSnapshot(){return En(this,mn,"f")}async finalRunSteps(){return await this.done(),Object.values(En(this,an,"f"))}async finalMessages(){return await this.done(),Object.values(En(this,on,"f"))}async finalRun(){if(await this.done(),!En(this,ln,"f"))throw Error("Final run was not received.");return En(this,ln,"f")}async _createThreadAssistantStream(e,t,r){const n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));const s={...t,stream:!0},i=await e.createAndRun(s,{...r,signal:this.controller.signal});this._connected();for await(const e of i)En(this,nn,"m",wn).call(this,e);if(i.controller.signal?.aborted)throw new X;return this._addRun(En(this,nn,"m",yn).call(this))}async _createAssistantStream(e,t,r,n){const s=n?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));const i={...r,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});this._connected();for await(const e of a)En(this,nn,"m",wn).call(this,e);if(a.controller.signal?.aborted)throw new X;return this._addRun(En(this,nn,"m",yn).call(this))}static accumulateDelta(e,t){for(const[r,n]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=n;continue}let t=e[r];if(null!=t)if("index"!==r&&"type"!==r){if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else{if(!rt(t)||!rt(n)){if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(const e of n){if(!rt(e))throw new Error(`Expected array delta entry to be an object but got: ${e}`);const r=e.index;if(null==r)throw console.error(e),new Error("Expected array delta entry to have an `index` property");if("number"!=typeof r)throw new Error(`Expected array delta entry \`index\` property to be a number but got ${r}`);const n=t[r];null==n?t.push(e):t[r]=this.accumulateDelta(n,e)}continue}throw Error(`Unhandled record type: ${r}, deltaValue: ${n}, accValue: ${t}`)}t=this.accumulateDelta(t,n)}e[r]=t}else e[r]=n;else e[r]=n}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,n){return await this._createAssistantStream(t,e,r,n)}async _runToolAssistantStream(e,t,r,n,s){return await this._createToolAssistantStream(r,e,t,n,s)}}wn=function(e){if(!this.ended)switch(Cn(this,fn,e,"f"),En(this,nn,"m",vn).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":En(this,nn,"m",An).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":En(this,nn,"m",Sn).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":En(this,nn,"m",_n).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},yn=function(){if(this.ended)throw new J("stream has ended, this shouldn't happen");if(!En(this,ln,"f"))throw Error("Final run has not been received");return En(this,ln,"f")},_n=function(e){const[t,r]=En(this,nn,"m",xn).call(this,e,En(this,cn,"f"));Cn(this,cn,t,"f"),En(this,on,"f")[t.id]=t;for(const e of r){const r=t.content[e.index];"text"==r?.type&&this._emit("textCreated",r.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(const r of e.data.delta.content){if("text"==r.type&&r.text){let e=r.text,n=t.content[r.index];if(!n||"text"!=n.type)throw Error("The snapshot associated with this text delta is not text or missing");this._emit("textDelta",e,n.text)}if(r.index!=En(this,un,"f")){if(En(this,hn,"f"))switch(En(this,hn,"f").type){case"text":this._emit("textDone",En(this,hn,"f").text,En(this,cn,"f"));break;case"image_file":this._emit("imageFileDone",En(this,hn,"f").image_file,En(this,cn,"f"))}Cn(this,un,r.index,"f")}Cn(this,hn,t.content[r.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==En(this,un,"f")){const t=e.data.content[En(this,un,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,En(this,cn,"f"));break;case"text":this._emit("textDone",t.text,En(this,cn,"f"))}}En(this,cn,"f")&&this._emit("messageDone",e.data),Cn(this,cn,void 0,"f")}},Sn=function(e){const t=En(this,nn,"m",bn).call(this,e);switch(Cn(this,mn,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const r=e.data.delta;if(r.step_details&&"tool_calls"==r.step_details.type&&r.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(const e of r.step_details.tool_calls)e.index==En(this,pn,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(En(this,dn,"f")&&this._emit("toolCallDone",En(this,dn,"f")),Cn(this,pn,e.index,"f"),Cn(this,dn,t.step_details.tool_calls[e.index],"f"),En(this,dn,"f")&&this._emit("toolCallCreated",En(this,dn,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":Cn(this,mn,void 0,"f"),"tool_calls"==e.data.step_details.type&&En(this,dn,"f")&&(this._emit("toolCallDone",En(this,dn,"f")),Cn(this,dn,void 0,"f")),this._emit("runStepDone",e.data,t)}},vn=function(e){En(this,sn,"f").push(e),this._emit("event",e)},bn=function(e){switch(e.event){case"thread.run.step.created":return En(this,an,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=En(this,an,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){const n=Pn.accumulateDelta(t,r.delta);En(this,an,"f")[e.data.id]=n}return En(this,an,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":En(this,an,"f")[e.data.id]=e.data}if(En(this,an,"f")[e.data.id])return En(this,an,"f")[e.data.id];throw new Error("No snapshot available")},xn=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(const e of n.delta.content)if(e.index in t.content){let r=t.content[e.index];t.content[e.index]=En(this,nn,"m",kn).call(this,e,r)}else t.content[e.index]=e,r.push(e);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},kn=function(e,t){return Pn.accumulateDelta(t,e)},An=function(e){switch(Cn(this,gn,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.cancelling":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":Cn(this,ln,e.data,"f"),En(this,dn,"f")&&(this._emit("toolCallDone",En(this,dn,"f")),Cn(this,dn,void 0,"f"))}};class In extends nt{create(e,t,r){return this._client.post(`/threads/${e}/messages`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/messages/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/messages`,Tn,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t,r){return this._client.delete(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class Tn extends ot{}In.MessagesPage=Tn;class On extends nt{retrieve(e,t,r,n={},s){return Ve(n)?this.retrieve(e,t,r,{},n):this._client.get(`/threads/${e}/runs/${t}/steps/${r}`,{query:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s?.headers}})}list(e,t,r={},n){return Ve(r)?this.list(e,t,{},r):this._client.getAPIList(`/threads/${e}/runs/${t}/steps`,Rn,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}}class Rn extends ot{}On.RunStepsPage=Rn;class Nn extends nt{constructor(){super(...arguments),this.steps=new On(this._client)}create(e,t,r){const{include:n,...s}=t;return this._client.post(`/threads/${e}/runs`,{query:{include:n},body:s,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers},stream:t.stream??!1})}retrieve(e,t,r){return this._client.get(`/threads/${e}/runs/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/runs`,jn,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}cancel(e,t,r){return this._client.post(`/threads/${e}/runs/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){const n=await this.create(e,t,r);return await this.poll(e,n.id,r)}createAndStream(e,t,r){return Pn.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){const n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){const{data:s,response:i}=await this.retrieve(e,t,{...r,headers:{...r?.headers,...n}}).withResponse();switch(s.status){case"queued":case"in_progress":case"cancelling":let e=5e3;if(r?.pollIntervalMs)e=r.pollIntervalMs;else{const t=i.headers.get("openai-poll-after-ms");if(t){const r=parseInt(t);isNaN(r)||(e=r)}}await He(e);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return s}}}stream(e,t,r){return Pn.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}/submit_tool_outputs`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers},stream:r.stream??!1})}async submitToolOutputsAndPoll(e,t,r,n){const s=await this.submitToolOutputs(e,t,r,n);return await this.poll(e,s.id,n)}submitToolOutputsStream(e,t,r,n){return Pn.createToolAssistantStream(e,t,this._client.beta.threads.runs,r,n)}}class jn extends ot{}Nn.RunsPage=jn,Nn.Steps=On,Nn.RunStepsPage=Rn;class Ln extends nt{constructor(){super(...arguments),this.runs=new Nn(this._client),this.messages=new In(this._client)}create(e={},t){return Ve(e)?this.create({},e):this._client.post("/threads",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/threads/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t){return this._client.delete(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers},stream:e.stream??!1})}async createAndRunPoll(e,t){const r=await this.createAndRun(e,t);return await this.runs.poll(r.thread_id,r.id,t)}createAndRunStream(e,t){return Pn.createThreadAssistantStream(e,this._client.beta.threads,t)}}Ln.Runs=Nn,Ln.RunsPage=jn,Ln.Messages=In,Ln.MessagesPage=Tn;class Dn extends nt{constructor(){super(...arguments),this.realtime=new rn(this._client),this.chat=new Yr(this._client),this.assistants=new qt(this._client),this.threads=new Ln(this._client)}}Dn.Realtime=rn,Dn.Assistants=qt,Dn.AssistantsPage=Ht,Dn.Threads=Ln;class Vn extends nt{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(`/batches/${e}`,t)}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/batches",Mn,{query:e,...t})}cancel(e,t){return this._client.post(`/batches/${e}/cancel`,t)}}class Mn extends ot{}Vn.BatchesPage=Mn;class Bn extends nt{create(e,t,r){return this._client.post(`/uploads/${e}/parts`,Ae({body:t,...r}))}}class Fn extends nt{constructor(){super(...arguments),this.parts=new Bn(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(`/uploads/${e}/complete`,{body:t,...r})}}function $n(e,t){const r=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:Wn(t,e)};if("message"===e.type){const r=e.content.map(e=>"output_text"===e.type?{...e,parsed:Un(t,e.text)}:e);return{...e,content:r}}return e}),n=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||qn(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(const e of n.output)if("message"===e.type)for(const t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed;return null}}),n}function Un(e,t){if("json_schema"!==e.text?.format?.type)return null;if("$parseRaw"in e.text?.format){const r=e.text?.format;return r.$parseRaw(t)}return JSON.parse(t)}function Wn(e,t){const r=(n=e.tools??[],s=t.name,n.find(e=>"function"===e.type&&e.name===s));var n,s,i;return{...t,...t,parsed_arguments:(i=r,"auto-parseable-tool"===i?.$brand?r.$parseRaw(t.arguments):r?.strict?JSON.parse(t.arguments):null)}}function qn(e){const t=[];for(const r of e.output)if("message"===r.type)for(const e of r.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}Fn.Parts=Bn;class Hn extends nt{list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/responses/${e}/input_items`,is,{query:t,...r})}}var Kn,zn,Jn,Gn,Xn,Zn,Qn,Yn,es,ts=function(e,t,r,n,s){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},rs=function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class ns extends hr{constructor(e){super(),Kn.add(this),zn.set(this,void 0),Jn.set(this,void 0),Gn.set(this,void 0),ts(this,zn,e,"f")}static createResponse(e,t,r){const n=new ns(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,r){const n=r?.signal;let s;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),rs(this,Kn,"m",Xn).call(this);let i=null;"response_id"in t?(s=await e.responses.retrieve(t.response_id,{stream:!0},{...r,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):s=await e.responses.create({...t,stream:!0},{...r,signal:this.controller.signal}),this._connected();for await(const e of s)rs(this,Kn,"m",Zn).call(this,e,i);if(s.controller.signal?.aborted)throw new X;return rs(this,Kn,"m",Qn).call(this)}[(zn=new WeakMap,Jn=new WeakMap,Gn=new WeakMap,Kn=new WeakSet,Xn=function(){this.ended||ts(this,Jn,void 0,"f")},Zn=function(e,t){if(this.ended)return;const r=(e,r)=>{(null==t||r.sequence_number>t)&&this._emit(e,r)},n=rs(this,Kn,"m",Yn).call(this,e);switch(r("event",e),e.type){case"response.output_text.delta":{const t=n.output[e.output_index];if(!t)throw new J(`missing output at index ${e.output_index}`);if("message"===t.type){const n=t.content[e.content_index];if(!n)throw new J(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new J(`expected content to be 'output_text', got ${n.type}`);r("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{const t=n.output[e.output_index];if(!t)throw new J(`missing output at index ${e.output_index}`);"function_call"===t.type&&r("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:r(e.type,e)}},Qn=function(){if(this.ended)throw new J("stream has ended, this shouldn't happen");const e=rs(this,Jn,"f");if(!e)throw new J("request ended without sending any events");ts(this,Jn,void 0,"f");const t=function(e,t){return function(e,t){return t&&function(e){return!!pr(e.text?.format)}(t)?$n(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,t)}(e,rs(this,zn,"f"));return ts(this,Gn,t,"f"),t},Yn=function(e){let t=rs(this,Jn,"f");if(!t){if("response.created"!==e.type)throw new J(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return t=ts(this,Jn,e.response,"f"),t}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{const r=t.output[e.output_index];if(!r)throw new J(`missing output at index ${e.output_index}`);"message"===r.type&&r.content.push(e.part);break}case"response.output_text.delta":{const r=t.output[e.output_index];if(!r)throw new J(`missing output at index ${e.output_index}`);if("message"===r.type){const t=r.content[e.content_index];if(!t)throw new J(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new J(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{const r=t.output[e.output_index];if(!r)throw new J(`missing output at index ${e.output_index}`);"function_call"===r.type&&(r.arguments+=e.delta);break}case"response.completed":ts(this,Jn,e.response,"f")}return t},Symbol.asyncIterator)](){const e=[],t=[];let r=!1;return this.on("event",r=>{const n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{r=!0;for(const e of t)e.resolve(void 0);t.length=0}),this.on("abort",e=>{r=!0;for(const r of t)r.reject(e);t.length=0}),this.on("error",e=>{r=!0;for(const r of t)r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const e=rs(this,Gn,"f");if(!e)throw new J("stream ended without producing a ChatCompletion");return e}}class ss extends nt{constructor(){super(...arguments),this.inputItems=new Hn(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&qn(e),e))}retrieve(e,t={},r){return this._client.get(`/responses/${e}`,{query:t,...r,stream:t?.stream??!1})}del(e,t){return this._client.delete(`/responses/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>$n(t,e))}stream(e,t){return ns.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(`/responses/${e}/cancel`,{...t,headers:{Accept:"*/*",...t?.headers}})}}class is extends ot{}ss.InputItems=Hn;class as extends nt{retrieve(e,t,r,n){return this._client.get(`/evals/${e}/runs/${t}/output_items/${r}`,n)}list(e,t,r={},n){return Ve(r)?this.list(e,t,{},r):this._client.getAPIList(`/evals/${e}/runs/${t}/output_items`,os,{query:r,...n})}}class os extends ot{}as.OutputItemListResponsesPage=os;class cs extends nt{constructor(){super(...arguments),this.outputItems=new as(this._client)}create(e,t,r){return this._client.post(`/evals/${e}/runs`,{body:t,...r})}retrieve(e,t,r){return this._client.get(`/evals/${e}/runs/${t}`,r)}list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/evals/${e}/runs`,ls,{query:t,...r})}del(e,t,r){return this._client.delete(`/evals/${e}/runs/${t}`,r)}cancel(e,t,r){return this._client.post(`/evals/${e}/runs/${t}`,r)}}class ls extends ot{}cs.RunListResponsesPage=ls,cs.OutputItems=as,cs.OutputItemListResponsesPage=os;class us extends nt{constructor(){super(...arguments),this.runs=new cs(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(`/evals/${e}`,t)}update(e,t,r){return this._client.post(`/evals/${e}`,{body:t,...r})}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/evals",hs,{query:e,...t})}del(e,t){return this._client.delete(`/evals/${e}`,t)}}class hs extends ot{}us.EvalListResponsesPage=hs,us.Runs=cs,us.RunListResponsesPage=ls;class ps extends nt{retrieve(e,t,r){return this._client.get(`/containers/${e}/files/${t}/content`,{...r,headers:{Accept:"application/binary",...r?.headers},__binaryResponse:!0})}}class ds extends nt{constructor(){super(...arguments),this.content=new ps(this._client)}create(e,t,r){return this._client.post(`/containers/${e}/files`,Ae({body:t,...r}))}retrieve(e,t,r){return this._client.get(`/containers/${e}/files/${t}`,r)}list(e,t={},r){return Ve(t)?this.list(e,{},t):this._client.getAPIList(`/containers/${e}/files`,fs,{query:t,...r})}del(e,t,r){return this._client.delete(`/containers/${e}/files/${t}`,{...r,headers:{Accept:"*/*",...r?.headers}})}}class fs extends ot{}ds.FileListResponsesPage=fs,ds.Content=ps;class gs extends nt{constructor(){super(...arguments),this.files=new ds(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(`/containers/${e}`,t)}list(e={},t){return Ve(e)?this.list({},e):this._client.getAPIList("/containers",ms,{query:e,...t})}del(e,t){return this._client.delete(`/containers/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}}class ms extends ot{}gs.ContainerListResponsesPage=ms,gs.Files=ds,gs.FileListResponsesPage=fs;class ws extends Re{constructor({baseURL:e=Je("OPENAI_BASE_URL"),apiKey:t=Je("OPENAI_API_KEY"),organization:r=Je("OPENAI_ORG_ID")??null,project:n=Je("OPENAI_PROJECT_ID")??null,...s}={}){if(void 0===t)throw new J("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const i={apiKey:t,organization:r,project:n,...s,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new J("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");super({baseURL:i.baseURL,timeout:i.timeout??6e5,httpAgent:i.httpAgent,maxRetries:i.maxRetries,fetch:i.fetch}),this.completions=new st(this),this.chat=new ht(this),this.embeddings=new pt(this),this.files=new dt(this),this.images=new gt(this),this.audio=new _t(this),this.moderations=new St(this),this.models=new vt(this),this.fineTuning=new jt(this),this.graders=new Dt(this),this.vectorStores=new $t(this),this.beta=new Dn(this),this.batches=new Vn(this),this.uploads=new Fn(this),this.responses=new ss(this),this.evals=new us(this),this.containers=new gs(this),this._options=i,this.apiKey=t,this.organization=r,this.project=n}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return function(e,t={}){let r=e;const n=function(e=P){if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");const t=e.charset||P.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=m;if(void 0!==e.format){if(!b.call(w,e.format))throw new TypeError("Unknown format option provided.");r=e.format}const n=w[r];let s,i=P.filter;if(("function"==typeof e.filter||k(e.filter))&&(i=e.filter),s=e.arrayFormat&&e.arrayFormat in x?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":P.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const a=void 0===e.allowDots?1==!!e.encodeDotInKeys||P.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:P.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:P.allowEmptyArrays,arrayFormat:s,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:P.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?P.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:P.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:P.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:P.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:P.encodeValuesOnly,filter:i,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:P.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:P.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:P.strictNullHandling}}(t);let s,i;"function"==typeof n.filter?(i=n.filter,r=i("",r)):k(n.filter)&&(i=n.filter,s=i);const a=[];if("object"!=typeof r||null===r)return"";const o=x[n.arrayFormat],c="comma"===o&&n.commaRoundTrip;s||(s=Object.keys(r)),n.sort&&s.sort(n.sort);const l=new WeakMap;for(let e=0;e<s.length;++e){const t=s[e];n.skipNulls&&null===r[t]||E(a,T(r[t],t,o,c,n.allowEmptyArrays,n.strictNullHandling,n.skipNulls,n.encodeDotInKeys,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,l))}const u=a.join(n.delimiter);let h=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}}es=ws,ws.OpenAI=es,ws.DEFAULT_TIMEOUT=6e5,ws.OpenAIError=J,ws.APIError=G,ws.APIConnectionError=Z,ws.APIConnectionTimeoutError=Q,ws.APIUserAbortError=X,ws.NotFoundError=re,ws.ConflictError=ne,ws.RateLimitError=ie,ws.BadRequestError=Y,ws.AuthenticationError=ee,ws.InternalServerError=ae,ws.PermissionDeniedError=te,ws.UnprocessableEntityError=se,ws.toFile=ve,ws.fileFromPath=B,ws.Completions=st,ws.Chat=ht,ws.ChatCompletionsPage=lt,ws.Embeddings=pt,ws.Files=dt,ws.FileObjectsPage=ft,ws.Images=gt,ws.Audio=_t,ws.Moderations=St,ws.Models=vt,ws.ModelsPage=bt,ws.FineTuning=jt,ws.Graders=Dt,ws.VectorStores=$t,ws.VectorStoresPage=Ut,ws.VectorStoreSearchResponsesPage=Wt,ws.Beta=Dn,ws.Batches=Vn,ws.BatchesPage=Mn,ws.Uploads=Fn,ws.Responses=ss,ws.Evals=us,ws.EvalListResponsesPage=hs,ws.Containers=gs,ws.ContainerListResponsesPage=ms,new Set(["/completions","/chat/completions","/embeddings","/audio/transcriptions","/audio/translations","/audio/speech","/images/generations","/images/edits"]);const ys=ws;function _s(){var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",s=r.toStringTag||"@@toStringTag";function i(r,n,s,i){var c=n&&n.prototype instanceof o?n:o,l=Object.create(c.prototype);return Ss(l,"_invoke",function(r,n,s){var i,o,c,l=0,u=s||[],h=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,r){return i=t,o=0,c=e,p.n=r,a}};function d(r,n){for(o=r,c=n,t=0;!h&&l&&!s&&t<u.length;t++){var s,i=u[t],d=p.p,f=i[2];r>3?(s=f===n)&&(c=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=d&&((s=r<2&&d<i[1])?(o=0,p.v=n,p.n=i[1]):d<f&&(s=r<3||i[0]>n||n>f)&&(i[4]=r,i[5]=n,p.n=f,o=0))}if(s||r>1)return a;throw h=!0,n}return function(s,u,f){if(l>1)throw TypeError("Generator is already running");for(h&&1===u&&d(u,f),o=u,c=f;(t=o<2?e:c)||!h;){i||(o?o<3?(o>1&&(p.n=-1),d(o,c)):p.n=c:p.v=c);try{if(l=2,i){if(o||(s="next"),t=i[s]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(c=TypeError("The iterator does not provide a '"+s+"' method"),o=1);i=e}else if((t=(h=p.n<0)?c:r.call(n,p))!==a)break}catch(t){i=e,o=1,c=t}finally{l=1}}return{value:t,done:h}}}(r,s,i),!0),l}var a={};function o(){}function c(){}function l(){}t=Object.getPrototypeOf;var u=[][n]?t(t([][n]())):(Ss(t={},n,function(){return this}),t),h=l.prototype=o.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,Ss(e,s,"GeneratorFunction")),e.prototype=Object.create(h),e}return c.prototype=l,Ss(h,"constructor",l),Ss(l,"constructor",c),c.displayName="GeneratorFunction",Ss(l,s,"GeneratorFunction"),Ss(h),Ss(h,s,"Generator"),Ss(h,n,function(){return this}),Ss(h,"toString",function(){return"[object Generator]"}),(_s=function(){return{w:i,m:p}})()}function Ss(e,t,r,n){var s=Object.defineProperty;try{s({},"",{})}catch(e){s=0}Ss=function(e,t,r,n){if(t)s?s(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r;else{var i=function(t,r){Ss(e,t,function(e){return this._invoke(t,r,e)})};i("next",0),i("throw",1),i("return",2)}},Ss(e,t,r,n)}function vs(e,t,r,n,s,i,a){try{var o=e[i](a),c=o.value}catch(e){return void r(e)}o.done?t(c):Promise.resolve(c).then(n,s)}function bs(e){return function(){var t=this,r=arguments;return new Promise(function(n,s){var i=e.apply(t,r);function a(e){vs(i,n,s,a,o,"next",e)}function o(e){vs(i,n,s,a,o,"throw",e)}a(void 0)})}}function xs(){return ks.apply(this,arguments)}function ks(){return(ks=bs(_s().m(function e(){return _s().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e,t){chrome.storage.sync.get(["AI"],function(r){r&&r.AI?e(r.AI):t(new Error("未找到AI配置，请在设置页面配置AI参数"))})}))},e)}))).apply(this,arguments)}function As(){return(As=bs(_s().m(function e(t,r){var n,s,i,a,o,c,l,u,h,p;return _s().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,xs();case 1:if(n=e.v,s=n.AIbaseURL,i=n.AIapiKey,a=n.AImodel,o=n.AISystemPrompt,c=n.AIUserPrompt,s&&i&&a&&o&&c){e.n=2;break}throw new Error("AI配置不完整，请在设置页面填写所有AI参数（AIbaseURL、AIapiKey、AImodel、AISystemPrompt、AIUserPrompt）");case 2:return l=new ys({apiKey:i,baseURL:s}),u=c.replace("{url}",r),e.n=3,l.completions.create({model:a,messages:[{role:"system",content:o},{role:"user",content:t+"\n\n"+u}]});case 3:return h=e.v,console.log(h.choices[0].message),e.a(2,h.choices[0].message.content);case 4:throw e.p=4,p=e.v,console.error("AI请求失败:",p),p;case 5:return e.a(2)}},e,null,[[0,4]])}))).apply(this,arguments)}function Es(e,t,r){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,r)}function Cs(e,t){return e.get(Is(e,t))}function Ps(e,t,r){return e.set(Is(e,t),r),r}function Is(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw new TypeError("Private element is not present on this object")}function Ts(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Os(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ts(Object(r),!0).forEach(function(t){Vs(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ts(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Rs(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Bs(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,s=function(){};return{s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw i}}}}function Ns(){var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",s=r.toStringTag||"@@toStringTag";function i(r,n,s,i){var c=n&&n.prototype instanceof o?n:o,l=Object.create(c.prototype);return js(l,"_invoke",function(r,n,s){var i,o,c,l=0,u=s||[],h=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,r){return i=t,o=0,c=e,p.n=r,a}};function d(r,n){for(o=r,c=n,t=0;!h&&l&&!s&&t<u.length;t++){var s,i=u[t],d=p.p,f=i[2];r>3?(s=f===n)&&(c=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=d&&((s=r<2&&d<i[1])?(o=0,p.v=n,p.n=i[1]):d<f&&(s=r<3||i[0]>n||n>f)&&(i[4]=r,i[5]=n,p.n=f,o=0))}if(s||r>1)return a;throw h=!0,n}return function(s,u,f){if(l>1)throw TypeError("Generator is already running");for(h&&1===u&&d(u,f),o=u,c=f;(t=o<2?e:c)||!h;){i||(o?o<3?(o>1&&(p.n=-1),d(o,c)):p.n=c:p.v=c);try{if(l=2,i){if(o||(s="next"),t=i[s]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(c=TypeError("The iterator does not provide a '"+s+"' method"),o=1);i=e}else if((t=(h=p.n<0)?c:r.call(n,p))!==a)break}catch(t){i=e,o=1,c=t}finally{l=1}}return{value:t,done:h}}}(r,s,i),!0),l}var a={};function o(){}function c(){}function l(){}t=Object.getPrototypeOf;var u=[][n]?t(t([][n]())):(js(t={},n,function(){return this}),t),h=l.prototype=o.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,js(e,s,"GeneratorFunction")),e.prototype=Object.create(h),e}return c.prototype=l,js(h,"constructor",l),js(l,"constructor",c),c.displayName="GeneratorFunction",js(l,s,"GeneratorFunction"),js(h),js(h,s,"Generator"),js(h,n,function(){return this}),js(h,"toString",function(){return"[object Generator]"}),(Ns=function(){return{w:i,m:p}})()}function js(e,t,r,n){var s=Object.defineProperty;try{s({},"",{})}catch(e){s=0}js=function(e,t,r,n){if(t)s?s(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r;else{var i=function(t,r){js(e,t,function(e){return this._invoke(t,r,e)})};i("next",0),i("throw",1),i("return",2)}},js(e,t,r,n)}function Ls(e,t,r,n,s,i,a){try{var o=e[i](a),c=o.value}catch(e){return void r(e)}o.done?t(c):Promise.resolve(c).then(n,s)}function Ds(e){return function(){var t=this,r=arguments;return new Promise(function(n,s){var i=e.apply(t,r);function a(e){Ls(i,n,s,a,o,"next",e)}function o(e){Ls(i,n,s,a,o,"throw",e)}a(void 0)})}}function Vs(e,t,r){return(t=Hs(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ms(e){return function(e){if(Array.isArray(e))return Fs(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Bs(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bs(e,t){if(e){if("string"==typeof e)return Fs(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fs(e,t):void 0}}function Fs(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function $s(e){return $s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$s(e)}function Us(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ws(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hs(n.key),n)}}function qs(e,t,r){return t&&Ws(e.prototype,t),r&&Ws(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Hs(e){var t=function(e){if("object"!=$s(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=$s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==$s(t)?t:t+""}function Ks(e,t,r){return t=Ys(t),function(e,t){if(t&&("object"==$s(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return zs(e)}(e,Zs()?Reflect.construct(t,r||[],Ys(e).constructor):t.apply(e,r))}function zs(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Js(){return Js="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=Ys(e)););return e}(e,t);if(n){var s=Object.getOwnPropertyDescriptor(n,t);return s.get?s.get.call(arguments.length<3?e:r):s.value}},Js.apply(null,arguments)}function Gs(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qs(e,t)}function Xs(e){var t="function"==typeof Map?new Map:void 0;return Xs=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(Zs())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var s=new(e.bind.apply(e,n));return r&&Qs(s,r.prototype),s}(e,arguments,Ys(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),Qs(r,e)},Xs(e)}function Zs(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Zs=function(){return!!e})()}function Qs(e,t){return Qs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Qs(e,t)}function Ys(e){return Ys=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ys(e)}var ei;function ti(e){return e&&e.__esModule?e.default:e}var ri="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==r.g?r.g:{},ni={},si={},ii=ri.parcelRequiref725;null==ii&&((ii=function(e){if(e in ni)return ni[e].exports;if(e in si){var t=si[e];delete si[e];var r={id:e,exports:{}};return ni[e]=r,t.call(r.exports,r,r.exports),r.exports}var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}).register=function(e,t){si[e]=t},ri.parcelRequiref725=ii),(0,ii.register)("3OecN",function(e,t){var n;"undefined"!=typeof globalThis||"undefined"!=typeof self&&self,n=function(e){if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw Error("This script should only be loaded in a browser extension.");globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id?e.exports=globalThis.browser:e.exports=function(e){var t={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(t).length)throw Error("api-metadata.json has not been included in browser-polyfill");var r=function(e){function t(e,r){var n;return Us(this,t),(n=Ks(this,t,[r])).createItem=e,n}return Gs(t,e),qs(t,[{key:"get",value:function(e){return this.has(e)||this.set(e,this.createItem(e)),function(e,t,r){var n=Js(Ys(e.prototype),"get",r);return"function"==typeof n?function(e){return n.apply(r,e)}:n}(t,0,this)([e])}}])}(Xs(WeakMap)),n=function(t,r){return function(){for(var n=arguments.length,s=new Array(n),i=0;i<n;i++)s[i]=arguments[i];e.runtime.lastError?t.reject(Error(e.runtime.lastError.message)):r.singleCallbackArg||s.length<=1&&!1!==r.singleCallbackArg?t.resolve(s[0]):t.resolve(s)}},s=function(e){return 1==e?"argument":"arguments"},i=function(e,t){return function(r){for(var i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];if(a.length<t.minArgs)throw Error("Expected at least ".concat(t.minArgs," ").concat(s(t.minArgs)," for ").concat(e,"(), got ").concat(a.length));if(a.length>t.maxArgs)throw Error("Expected at most ".concat(t.maxArgs," ").concat(s(t.maxArgs)," for ").concat(e,"(), got ").concat(a.length));return new Promise(function(s,i){if(t.fallbackToNoCallback)try{r[e].apply(r,a.concat([n({resolve:s,reject:i},t)]))}catch(n){console.warn("".concat(e," API method doesn't seem to support the callback parameter, falling back to call it without a callback: "),n),r[e].apply(r,a),t.fallbackToNoCallback=!1,t.noCallback=!0,s()}else t.noCallback?(r[e].apply(r,a),s()):r[e].apply(r,a.concat([n({resolve:s,reject:i},t)]))})}},a=function(e,t,r){return new Proxy(t,{apply:function(t,n,s){return r.call.apply(r,[n,e].concat(Ms(s)))}})},o=Function.call.bind(Object.prototype.hasOwnProperty),c=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=Object.create(null);return new Proxy(Object.create(e),{has:function(t,r){return r in e||r in n},get:function(s,l,u){if(l in n)return n[l];if(l in e){var h=e[l];if("function"==typeof h)if("function"==typeof t[l])h=a(e,e[l],t[l]);else if(o(r,l)){var p=i(l,r[l]);h=a(e,e[l],p)}else h=h.bind(e);else if("object"==$s(h)&&null!==h&&(o(t,l)||o(r,l)))h=c(h,t[l],r[l]);else{if(!o(r,"*"))return Object.defineProperty(n,l,{configurable:!0,enumerable:!0,get:function(){return e[l]},set:function(t){e[l]=t}}),h;h=c(h,t[l],r["*"])}return n[l]=h,h}},set:function(t,r,s,i){return r in n?n[r]=s:e[r]=s,!0},defineProperty:function(e,t,r){return Reflect.defineProperty(n,t,r)},deleteProperty:function(e,t){return Reflect.deleteProperty(n,t)}})},l=function(e){return{addListener:function(t,r){for(var n=arguments.length,s=new Array(n>2?n-2:0),i=2;i<n;i++)s[i-2]=arguments[i];t.addListener.apply(t,[e.get(r)].concat(s))},hasListener:function(t,r){return t.hasListener(e.get(r))},removeListener:function(t,r){t.removeListener(e.get(r))}}},u=new r(function(e){return"function"!=typeof e?e:function(t){e(c(t,{},{getContent:{minArgs:0,maxArgs:0}}))}}),h=new r(function(e){return"function"!=typeof e?e:function(t,r,n){var s,i,a=!1,o=new Promise(function(e){s=function(t){a=!0,e(t)}});try{i=e(t,r,s)}catch(e){i=Promise.reject(e)}var c=!0!==i&&function(e){return e&&"object"==$s(e)&&"function"==typeof e.then}(i);return!(!0!==i&&!c&&!a||((c?i:o).then(function(e){n(e)},function(e){n({__mozWebExtensionPolyfillReject__:!0,message:e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred"})}).catch(function(e){console.error("Failed to send onMessage rejected reply",e)}),0))}}),p=function(t,r){var n=t.reject,s=t.resolve;e.runtime.lastError?"The message port closed before a response was received."===e.runtime.lastError.message?s():n(Error(e.runtime.lastError.message)):r&&r.__mozWebExtensionPolyfillReject__?n(Error(r.message)):s(r)},d=function(e,t,r){for(var n=arguments.length,i=new Array(n>3?n-3:0),a=3;a<n;a++)i[a-3]=arguments[a];if(i.length<t.minArgs)throw Error("Expected at least ".concat(t.minArgs," ").concat(s(t.minArgs)," for ").concat(e,"(), got ").concat(i.length));if(i.length>t.maxArgs)throw Error("Expected at most ".concat(t.maxArgs," ").concat(s(t.maxArgs)," for ").concat(e,"(), got ").concat(i.length));return new Promise(function(e,t){var n=p.bind(null,{resolve:e,reject:t});i.push(n),r.sendMessage.apply(r,i)})},f={devtools:{network:{onRequestFinished:l(u)}},runtime:{onMessage:l(h),onMessageExternal:l(h),sendMessage:d.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:d.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},g={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return t.privacy={network:{"*":g},services:{"*":g},websites:{"*":g}},c(e,f,t)}(chrome)},"function"==typeof define&&r.amdO?define("webextension-polyfill",["module"],n):n(e)});var ai=ii("3OecN"),oi=new TextEncoder,ci=new TextDecoder;function li(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n=new Uint8Array(t.reduce(function(e,t){return e+t.length},0)),s=0,i=0,a=t;i<a.length;i++){var o=a[i];n.set(o,s),s+=o.length}return n}function ui(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:ci.decode(e),{alphabet:"base64url"});var t=e;t instanceof Uint8Array&&(t=ci.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);for(var n=atob(r),s=new Uint8Array(n.length),i=0;i<n.length;i++)s[i]=n.charCodeAt(i);return s}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}}function hi(e){var t=e;return"string"==typeof t&&(t=oi.encode(t)),Uint8Array.prototype.toBase64?t.toBase64({alphabet:"base64url",omitPadding:!0}):function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();for(var t=[],r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))}(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}var pi=function(e){function t(e,r){var n,s;return Us(this,t),Vs(zs(s=Ks(this,t,[e,r])),"code","ERR_JOSE_GENERIC"),s.name=s.constructor.name,null===(n=Error.captureStackTrace)||void 0===n||n.call(Error,zs(s),s.constructor),s}return Gs(t,e),qs(t)}(Xs(Error));Vs(pi,"code","ERR_JOSE_GENERIC");var di=function(e){function t(e,r){var n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"unspecified",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unspecified";return Us(this,t),Vs(zs(n=Ks(this,t,[e,{cause:{claim:s,reason:i,payload:r}}])),"code","ERR_JWT_CLAIM_VALIDATION_FAILED"),Vs(zs(n),"claim",void 0),Vs(zs(n),"reason",void 0),Vs(zs(n),"payload",void 0),n.claim=s,n.reason=i,n.payload=r,n}return Gs(t,e),qs(t)}(pi);Vs(di,"code","ERR_JWT_CLAIM_VALIDATION_FAILED");var fi=function(e){function t(e,r){var n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"unspecified",i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unspecified";return Us(this,t),Vs(zs(n=Ks(this,t,[e,{cause:{claim:s,reason:i,payload:r}}])),"code","ERR_JWT_EXPIRED"),Vs(zs(n),"claim",void 0),Vs(zs(n),"reason",void 0),Vs(zs(n),"payload",void 0),n.claim=s,n.reason=i,n.payload=r,n}return Gs(t,e),qs(t)}(pi);Vs(fi,"code","ERR_JWT_EXPIRED");var gi=function(e){function t(){var e;Us(this,t);for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];return Vs(e=Ks(this,t,[].concat(n)),"code","ERR_JOSE_ALG_NOT_ALLOWED"),e}return Gs(t,e),qs(t)}(pi);Vs(gi,"code","ERR_JOSE_ALG_NOT_ALLOWED");var mi=function(e){function t(){var e;Us(this,t);for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];return Vs(e=Ks(this,t,[].concat(n)),"code","ERR_JOSE_NOT_SUPPORTED"),e}return Gs(t,e),qs(t)}(pi);Vs(mi,"code","ERR_JOSE_NOT_SUPPORTED");var wi=function(e){function t(){var e;Us(this,t);for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];return Vs(e=Ks(this,t,[].concat(n)),"code","ERR_JWS_INVALID"),e}return Gs(t,e),qs(t)}(pi);Vs(wi,"code","ERR_JWS_INVALID");var yi=function(e){function t(){var e;Us(this,t);for(var r=arguments.length,n=new Array(r),s=0;s<r;s++)n[s]=arguments[s];return Vs(e=Ks(this,t,[].concat(n)),"code","ERR_JWT_INVALID"),e}return Gs(t,e),qs(t)}(pi);Vs(yi,"code","ERR_JWT_INVALID");var _i=function(e){function t(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"multiple matching keys found in the JSON Web Key Set",n=arguments.length>1?arguments[1]:void 0;return Us(this,t),Vs(e=Ks(this,t,[r,n]),Symbol.asyncIterator,void 0),Vs(e,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS"),e}return Gs(t,e),qs(t)}(pi);Vs(_i,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS");var Si=function(e){function t(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"signature verification failed",n=arguments.length>1?arguments[1]:void 0;return Us(this,t),Vs(e=Ks(this,t,[r,n]),"code","ERR_JWS_SIGNATURE_VERIFICATION_FAILED"),e}return Gs(t,e),qs(t)}(pi);Vs(Si,"code","ERR_JWS_SIGNATURE_VERIFICATION_FAILED");var vi=function(e,t){var r="SHA-".concat(e.slice(-3));switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new mi("alg ".concat(e," is not supported either by JOSE or your javascript runtime"))}},bi=function(e,t){if(e.startsWith("RS")||e.startsWith("PS")){var r=t.algorithm.modulusLength;if("number"!=typeof r||r<2048)throw TypeError("".concat(e," requires key modulusLength to be 2048 bits or larger"))}};function xi(e){return TypeError("CryptoKey does not support this operation, its ".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"algorithm.name"," must be ").concat(e))}function ki(e,t){return e.name===t}function Ai(e){return parseInt(e.name.slice(4),10)}function Ei(e,t){for(var r,n=arguments.length,s=new Array(n>2?n-2:0),i=2;i<n;i++)s[i-2]=arguments[i];if((s=s.filter(Boolean)).length>2){var a=s.pop();e+="one of type ".concat(s.join(", "),", or ").concat(a,".")}else 2===s.length?e+="one of type ".concat(s[0]," or ").concat(s[1],"."):e+="of type ".concat(s[0],".");return null==t?e+=" Received ".concat(t):"function"==typeof t&&t.name?e+=" Received function ".concat(t.name):"object"==$s(t)&&null!=t&&(null===(r=t.constructor)||void 0===r?void 0:r.name)&&(e+=" Received an instance of ".concat(t.constructor.name)),e}var Ci=function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return Ei.apply(void 0,["Key must be ",e].concat(r))};function Pi(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),s=2;s<r;s++)n[s-2]=arguments[s];return Ei.apply(void 0,["Key for the ".concat(e," algorithm must be "),t].concat(n))}var Ii=function(){var e=Ds(Ns().m(function e(t,r,n){return Ns().w(function(e){for(;;)switch(e.n){case 0:if(!(r instanceof Uint8Array)){e.n=2;break}if(t.startsWith("HS")){e.n=1;break}throw TypeError(Ci(r,"CryptoKey","KeyObject","JSON Web Key"));case 1:return e.a(2,crypto.subtle.importKey("raw",r,{hash:"SHA-".concat(t.slice(-3)),name:"HMAC"},!1,[n]));case 2:return e.a(2,(function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":if(!ki(e.algorithm,"HMAC"))throw xi("HMAC");var n=parseInt(t.slice(2),10);if(Ai(e.algorithm.hash)!==n)throw xi("SHA-".concat(n),"algorithm.hash");break;case"RS256":case"RS384":case"RS512":if(!ki(e.algorithm,"RSASSA-PKCS1-v1_5"))throw xi("RSASSA-PKCS1-v1_5");var s=parseInt(t.slice(2),10);if(Ai(e.algorithm.hash)!==s)throw xi("SHA-".concat(s),"algorithm.hash");break;case"PS256":case"PS384":case"PS512":if(!ki(e.algorithm,"RSA-PSS"))throw xi("RSA-PSS");var i=parseInt(t.slice(2),10);if(Ai(e.algorithm.hash)!==i)throw xi("SHA-".concat(i),"algorithm.hash");break;case"Ed25519":case"EdDSA":if(!ki(e.algorithm,"Ed25519"))throw xi("Ed25519");break;case"ES256":case"ES384":case"ES512":if(!ki(e.algorithm,"ECDSA"))throw xi("ECDSA");var a=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==a)throw xi(a,"algorithm.namedCurve");break;default:throw TypeError("CryptoKey does not support this operation")}if(r&&!e.usages.includes(r))throw TypeError("CryptoKey does not support this operation, its usages must include ".concat(r,"."))}(r,t,n),r))}},e)}));return function(t,r,n){return e.apply(this,arguments)}}(),Ti=function(){var e=Ds(Ns().m(function e(t,r,n,s){var i,a;return Ns().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.n=1,Ii(t,r,"verify");case 1:return i=e.v,bi(t,i),a=vi(t,i.algorithm),e.p=2,e.n=3,crypto.subtle.verify(a,i,n,s);case 3:return e.a(2,e.v);case 4:return e.p=4,e.v,e.a(2,!1)}},e,null,[[2,4]])}));return function(t,r,n,s){return e.apply(this,arguments)}}(),Oi=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n,s=t.filter(Boolean);if(0===s.length||1===s.length)return!0;var i,a=Rs(s);try{for(a.s();!(i=a.n()).done;){var o=i.value,c=Object.keys(o);if(n&&0!==n.size)for(var l=0,u=c;l<u.length;l++){var h=u[l];if(n.has(h))return!1;n.add(h)}else n=new Set(c)}}catch(e){a.e(e)}finally{a.f()}return!0},Ri=function(e){if("object"!=$s(e)||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function Ni(e){return"CryptoKey"===(null==e?void 0:e[Symbol.toStringTag])}function ji(e){return"KeyObject"===(null==e?void 0:e[Symbol.toStringTag])}var Li=function(e){return Ni(e)||ji(e)};function Di(e){return Ri(e)&&"string"==typeof e.kty}var Vi=function(e){return null==e?void 0:e[Symbol.toStringTag]},Mi=function(e,t,r){if(void 0!==t.use){var n;switch(r){case"sign":case"verify":n="sig";break;case"encrypt":case"decrypt":n="enc"}if(t.use!==n)throw TypeError('Invalid key for this operation, its "use" must be "'.concat(n,'" when present'))}if(void 0!==t.alg&&t.alg!==e)throw TypeError('Invalid key for this operation, its "alg" must be "'.concat(e,'" when present'));if(Array.isArray(t.key_ops)){var s,i,a;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):a=r;break;case e.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):a=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):a="wrapKey";break;case"decrypt"===r:a=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(a&&!1===(null===(s=t.key_ops)||void 0===s||null===(i=s.includes)||void 0===i?void 0:i.call(s,a)))throw TypeError('Invalid key for this operation, its "key_ops" must include "'.concat(a,'" when present'))}return!0},Bi=function(e,t,r){e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?function(e,t,r){if(!(t instanceof Uint8Array)){if(Di(t)){if("oct"===t.kty&&"string"==typeof t.k&&Mi(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!Li(t))throw TypeError(Pi(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError("".concat(Vi(t),' instances for symmetric algorithms must be of type "secret"'))}}(e,t,r):function(e,t,r){if(Di(t))switch(r){case"decrypt":case"sign":if("oct"!==t.kty&&"string"==typeof t.d&&Mi(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if("oct"!==t.kty&&void 0===t.d&&Mi(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!Li(t))throw TypeError(Pi(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError("".concat(Vi(t),' instances for asymmetric algorithms must not be of type "secret"'));if("public"===t.type)switch(r){case"sign":throw TypeError("".concat(Vi(t),' instances for asymmetric algorithm signing must be of type "private"'));case"decrypt":throw TypeError("".concat(Vi(t),' instances for asymmetric algorithm decryption must be of type "private"'))}if("private"===t.type)switch(r){case"verify":throw TypeError("".concat(Vi(t),' instances for asymmetric algorithm verifying must be of type "public"'));case"encrypt":throw TypeError("".concat(Vi(t),' instances for asymmetric algorithm encryption must be of type "public"'))}}(e,t,r)},Fi=function(e,t,r,n,s){var i;if(void 0!==s.crit&&void 0===(null==n?void 0:n.crit))throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(function(e){return"string"!=typeof e||0===e.length}))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');var a,o=Rs((i=void 0!==r?new Map([].concat(Ms(Object.entries(r)),Ms(t.entries()))):t,n.crit));try{for(o.s();!(a=o.n()).done;){var c=a.value;if(!i.has(c))throw new mi('Extension Header Parameter "'.concat(c,'" is not recognized'));if(void 0===s[c])throw new e('Extension Header Parameter "'.concat(c,'" is missing'));if(i.get(c)&&void 0===n[c])throw new e('Extension Header Parameter "'.concat(c,'" MUST be integrity protected'))}}catch(e){o.e(e)}finally{o.f()}return new Set(n.crit)},$i=function(e,t){if(void 0!==t&&(!Array.isArray(t)||t.some(function(e){return"string"!=typeof e})))throw TypeError('"'.concat(e,'" option must be an array of strings'));if(t)return new Set(t)},Ui=function(){var e=Ds(Ns().m(function e(t){var r,n,s,i,a,o;return Ns().w(function(e){for(;;)switch(e.n){case 0:if(t.alg){e.n=1;break}throw TypeError('"alg" argument is required when "jwk.alg" is not present');case 1:return s=function(e){var t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:"SHA-".concat(e.alg.slice(-3))},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:"SHA-".concat(e.alg.slice(-3))},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:"SHA-".concat(parseInt(e.alg.slice(-3),10)||1)},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new mi('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new mi('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new mi('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new mi('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(t),i=s.algorithm,a=s.keyUsages,o=Os({},t),e.a(2,(delete o.alg,delete o.use,crypto.subtle.importKey("jwk",o,i,null!==(r=t.ext)&&void 0!==r?r:!t.d,null!==(n=t.key_ops)&&void 0!==n?n:a)))}},e)}));return function(t){return e.apply(this,arguments)}}(),Wi=function(){var e=Ds(Ns().m(function e(t,r,n){var s,i,a,o=arguments;return Ns().w(function(e){for(;;)switch(e.n){case 0:if(s=o.length>3&&void 0!==o[3]&&o[3],null==(i=(ei||(ei=new WeakMap)).get(t))||!i[n]){e.n=1;break}return e.a(2,i[n]);case 1:return e.n=2,Ui(Os(Os({},r),{},{alg:n}));case 2:return a=e.v,e.a(2,(s&&Object.freeze(t),i?i[n]=a:ei.set(t,Vs({},n,a)),a))}},e)}));return function(t,r,n){return e.apply(this,arguments)}}(),qi=function(e,t){var r,n=(ei||(ei=new WeakMap)).get(e);if(null!=n&&n[t])return n[t];var s="public"===e.type,i=!!s;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,i,s?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,i,[s?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){var a;switch(t){case"RSA-OAEP":a="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":a="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":a="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":a="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:a},i,s?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:a},i,[s?"verify":"sign"])}if("ec"===e.asymmetricKeyType){var o,c=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(null===(o=e.asymmetricKeyDetails)||void 0===o?void 0:o.namedCurve);if(!c)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===c&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:c},i,[s?"verify":"sign"])),"ES384"===t&&"P-384"===c&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:c},i,[s?"verify":"sign"])),"ES512"===t&&"P-521"===c&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:c},i,[s?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:c},i,s?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:ei.set(e,Vs({},t,r)),r},Hi=function(){var e=Ds(Ns().m(function e(t,r){var n,s;return Ns().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!(t instanceof Uint8Array||Ni(t))){e.n=1;break}return e.a(2,t);case 1:if(!ji(t)){e.n=6;break}if("secret"!==t.type){e.n=2;break}return e.a(2,t.export());case 2:if(!("toCryptoKey"in t)||"function"!=typeof t.toCryptoKey){e.n=5;break}return e.p=3,e.a(2,qi(t,r));case 4:if(e.p=4,!((s=e.v)instanceof TypeError)){e.n=5;break}throw s;case 5:return n=t.export({format:"jwk"}),e.a(2,Wi(t,n,r));case 6:if(!Di(t)){e.n=7;break}return e.a(2,t.k?ui(t.k):Wi(t,t,r,!0));case 7:throw Error("unreachable");case 8:return e.a(2)}},e,null,[[3,4]])}));return function(t,r){return e.apply(this,arguments)}}();function Ki(e,t,r){return zi.apply(this,arguments)}function zi(){return zi=Ds(Ns().m(function e(t,r,n){var s,i,a,o,c,l,u,h,p,d,f,g,m,w;return Ns().w(function(e){for(;;)switch(e.p=e.n){case 0:if(Ri(t)){e.n=1;break}throw new wi("Flattened JWS must be an object");case 1:if(void 0!==t.protected||void 0!==t.header){e.n=2;break}throw new wi('Flattened JWS must have either of the "protected" or "header" members');case 2:if(void 0===t.protected||"string"==typeof t.protected){e.n=3;break}throw new wi("JWS Protected Header incorrect type");case 3:if(void 0!==t.payload){e.n=4;break}throw new wi("JWS Payload missing");case 4:if("string"==typeof t.signature){e.n=5;break}throw new wi("JWS Signature missing or incorrect type");case 5:if(void 0===t.header||Ri(t.header)){e.n=6;break}throw new wi("JWS Unprotected Header incorrect type");case 6:if(o={},!t.protected){e.n=9;break}e.p=7,c=ui(t.protected),o=JSON.parse(ci.decode(c)),e.n=9;break;case 8:throw e.p=8,e.v,new wi("JWS Protected Header is invalid");case 9:if(Oi(o,t.header)){e.n=10;break}throw new wi("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");case 10:if(l=Os(Os({},o),t.header),u=Fi(wi,new Map([["b64",!0]]),null==n?void 0:n.crit,o,l),h=!0,!u.has("b64")||"boolean"==typeof(h=o.b64)){e.n=11;break}throw new wi('The "b64" (base64url-encode payload) Header Parameter must be a boolean');case 11:if("string"==typeof(p=l.alg)&&p){e.n=12;break}throw new wi('JWS "alg" (Algorithm) Header Parameter missing or invalid');case 12:if(!(d=n&&$i("algorithms",n.algorithms))||d.has(p)){e.n=13;break}throw new gi('"alg" (Algorithm) Header Parameter value not allowed');case 13:if(!h){e.n=15;break}if("string"==typeof t.payload){e.n=14;break}throw new wi("JWS Payload must be a string");case 14:e.n=16;break;case 15:if("string"==typeof t.payload||t.payload instanceof Uint8Array){e.n=16;break}throw new wi("JWS Payload must be a string or an Uint8Array instance");case 16:if(f=!1,"function"!=typeof r){e.n=18;break}return e.n=17,r(o,t);case 17:r=e.v,f=!0;case 18:Bi(p,r,"verify"),g=li(oi.encode(null!==(s=t.protected)&&void 0!==s?s:""),oi.encode("."),"string"==typeof t.payload?oi.encode(t.payload):t.payload),e.p=19,i=ui(t.signature),e.n=21;break;case 20:throw e.p=20,e.v,new wi("Failed to base64url decode the signature");case 21:return e.n=22,Hi(r,p);case 22:return m=e.v,e.n=23,Ti(p,m,i,g);case 23:if(e.v){e.n=24;break}throw new Si;case 24:if(!h){e.n=28;break}e.p=25,a=ui(t.payload),e.n=27;break;case 26:throw e.p=26,e.v,new wi("Failed to base64url decode the payload");case 27:e.n=29;break;case 28:a="string"==typeof t.payload?oi.encode(t.payload):t.payload;case 29:return w={payload:a},e.a(2,(void 0!==t.protected&&(w.protectedHeader=o),void 0!==t.header&&(w.unprotectedHeader=t.header),f?Os(Os({},w),{},{key:m}):w))}},e,null,[[25,26],[19,20],[7,8]])})),zi.apply(this,arguments)}function Ji(e,t,r){return Gi.apply(this,arguments)}function Gi(){return Gi=Ds(Ns().m(function e(t,r,n){var s,i,a,o,c,l;return Ns().w(function(e){for(;;)switch(e.n){case 0:if(t instanceof Uint8Array&&(t=ci.decode(t)),"string"==typeof t){e.n=1;break}throw new wi("Compact JWS must be a string or Uint8Array");case 1:if(s=t.split("."),i=s[0],a=s[1],o=s[2],3===s.length){e.n=2;break}throw new wi("Invalid Compact JWS");case 2:return e.n=3,Ki({payload:a,protected:i,signature:o},r,n);case 3:return c=e.v,l={payload:c.payload,protectedHeader:c.protectedHeader},e.a(2,"function"==typeof r?Os(Os({},l),{},{key:c.key}):l)}},e)})),Gi.apply(this,arguments)}var Xi=function(e){return Math.floor(e.getTime()/1e3)},Zi=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,Qi=function(e){var t,r=Zi.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");var n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(31557600*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function Yi(e,t){if(!Number.isFinite(t))throw TypeError("Invalid ".concat(e," input"));return t}var ea=function(e){return e.includes("/")?e.toLowerCase():"application/".concat(e.toLowerCase())},ta=function(e,t){return"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)))},ra=new WeakMap,na=function(){return qs(function e(t){if(Us(this,e),Es(this,ra,void 0),!Ri(t))throw TypeError("JWT Claims Set MUST be an object");Ps(ra,this,structuredClone(t))},[{key:"data",value:function(){return oi.encode(JSON.stringify(Cs(ra,this)))}},{key:"iss",get:function(){return Cs(ra,this).iss},set:function(e){Cs(ra,this).iss=e}},{key:"sub",get:function(){return Cs(ra,this).sub},set:function(e){Cs(ra,this).sub=e}},{key:"aud",get:function(){return Cs(ra,this).aud},set:function(e){Cs(ra,this).aud=e}},{key:"jti",set:function(e){Cs(ra,this).jti=e}},{key:"nbf",set:function(e){"number"==typeof e?Cs(ra,this).nbf=Yi("setNotBefore",e):e instanceof Date?Cs(ra,this).nbf=Yi("setNotBefore",Xi(e)):Cs(ra,this).nbf=Xi(new Date)+Qi(e)}},{key:"exp",set:function(e){"number"==typeof e?Cs(ra,this).exp=Yi("setExpirationTime",e):e instanceof Date?Cs(ra,this).exp=Yi("setExpirationTime",Xi(e)):Cs(ra,this).exp=Xi(new Date)+Qi(e)}},{key:"iat",set:function(e){void 0===e?Cs(ra,this).iat=Xi(new Date):e instanceof Date?Cs(ra,this).iat=Yi("setIssuedAt",Xi(e)):Cs(ra,this).iat=Yi("setIssuedAt","string"==typeof e?Xi(new Date)+Qi(e):e)}}])}();function sa(e,t,r){return ia.apply(this,arguments)}function ia(){return ia=Ds(Ns().m(function e(t,r,n){var s,i,a;return Ns().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,Ji(t,r,n);case 1:if(i=e.v,null===(s=i.protectedHeader.crit)||void 0===s||!s.includes("b64")||!1!==i.protectedHeader.b64){e.n=2;break}throw new yi("JWTs MUST NOT use unencoded payload");case 2:return a={payload:function(e,t){var r,n,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{r=JSON.parse(ci.decode(t))}catch(e){}if(!Ri(r))throw new yi("JWT Claims Set must be a top-level JSON object");var i=s.typ;if(i&&("string"!=typeof e.typ||ea(e.typ)!==ea(i)))throw new di('unexpected "typ" JWT header value',r,"typ","check_failed");var a,o=s.requiredClaims,c=void 0===o?[]:o,l=s.issuer,u=s.subject,h=s.audience,p=s.maxTokenAge,d=Ms(c),f=Rs((void 0!==p&&d.push("iat"),void 0!==h&&d.push("aud"),void 0!==u&&d.push("sub"),void 0!==l&&d.push("iss"),new Set(d.reverse())));try{for(f.s();!(a=f.n()).done;){var g=a.value;if(!(g in r))throw new di('missing required "'.concat(g,'" claim'),r,g,"missing")}}catch(e){f.e(e)}finally{f.f()}if(l&&!(Array.isArray(l)?l:[l]).includes(r.iss))throw new di('unexpected "iss" claim value',r,"iss","check_failed");if(u&&r.sub!==u)throw new di('unexpected "sub" claim value',r,"sub","check_failed");if(h&&!ta(r.aud,"string"==typeof h?[h]:h))throw new di('unexpected "aud" claim value',r,"aud","check_failed");switch($s(s.clockTolerance)){case"string":n=Qi(s.clockTolerance);break;case"number":n=s.clockTolerance;break;case"undefined":n=0;break;default:throw TypeError("Invalid clockTolerance option type")}var m=s.currentDate,w=Xi(m||new Date);if((void 0!==r.iat||p)&&"number"!=typeof r.iat)throw new di('"iat" claim must be a number',r,"iat","invalid");if(void 0!==r.nbf){if("number"!=typeof r.nbf)throw new di('"nbf" claim must be a number',r,"nbf","invalid");if(r.nbf>w+n)throw new di('"nbf" claim timestamp check failed',r,"nbf","check_failed")}if(void 0!==r.exp){if("number"!=typeof r.exp)throw new di('"exp" claim must be a number',r,"exp","invalid");if(r.exp<=w-n)throw new fi('"exp" claim timestamp check failed',r,"exp","check_failed")}if(p){var y=w-r.iat;if(y-n>("number"==typeof p?p:Qi(p)))throw new fi('"iat" claim timestamp check failed (too far in the past)',r,"iat","check_failed");if(y<0-n)throw new di('"iat" claim timestamp check failed (it should be in the past)',r,"iat","check_failed")}return r}(i.protectedHeader,i.payload,n),protectedHeader:i.protectedHeader},e.a(2,"function"==typeof r?Os(Os({},a),{},{key:i.key}):a)}},e)})),ia.apply(this,arguments)}var aa=function(){var e=Ds(Ns().m(function e(t,r,n){var s,i,a;return Ns().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,Ii(t,r,"sign");case 1:return s=e.v,bi(t,s),i=Uint8Array,e.n=2,crypto.subtle.sign(vi(t,s.algorithm),s,n);case 2:return a=e.v,e.a(2,new i(a))}},e)}));return function(t,r,n){return e.apply(this,arguments)}}(),oa=new WeakMap,ca=new WeakMap,la=new WeakMap,ua=function(){return qs(function e(t){if(Us(this,e),Es(this,oa,void 0),Es(this,ca,void 0),Es(this,la,void 0),!(t instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");Ps(oa,this,t)},[{key:"setProtectedHeader",value:function(e){if(Cs(ca,this))throw TypeError("setProtectedHeader can only be called once");return Ps(ca,this,e),this}},{key:"setUnprotectedHeader",value:function(e){if(Cs(la,this))throw TypeError("setUnprotectedHeader can only be called once");return Ps(la,this,e),this}},{key:"sign",value:(e=Ds(Ns().m(function e(t,r){var n,s,i,a,o,c,l,u,h,p,d,f;return Ns().w(function(e){for(;;)switch(e.n){case 0:if(Cs(ca,this)||Cs(la,this)){e.n=1;break}throw new wi("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");case 1:if(Oi(Cs(ca,this),Cs(la,this))){e.n=2;break}throw new wi("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");case 2:if(s=Os(Os({},Cs(ca,this)),Cs(la,this)),i=Fi(wi,new Map([["b64",!0]]),null==r?void 0:r.crit,Cs(ca,this),s),a=!0,!i.has("b64")||"boolean"==typeof(a=Cs(ca,this).b64)){e.n=3;break}throw new wi('The "b64" (base64url-encode payload) Header Parameter must be a boolean');case 3:if("string"==typeof(o=s.alg)&&o){e.n=4;break}throw new wi('JWS "alg" (Algorithm) Header Parameter missing or invalid');case 4:return Bi(o,t,"sign"),c=Cs(oa,this),a&&(c=oi.encode(hi(c))),l=li(n=Cs(ca,this)?oi.encode(hi(JSON.stringify(Cs(ca,this)))):oi.encode(""),oi.encode("."),c),e.n=5,Hi(t,o);case 5:return u=e.v,p=hi,e.n=6,aa(o,u,l);case 6:return d=e.v,f=p(d),h={signature:f,payload:""},e.a(2,(a&&(h.payload=ci.decode(c)),Cs(la,this)&&(h.header=Cs(la,this)),Cs(ca,this)&&(h.protected=ci.decode(n)),h))}},e,this)})),function(t,r){return e.apply(this,arguments)})}]);var e}(),ha=new WeakMap,pa=function(){return qs(function e(t){Us(this,e),Es(this,ha,void 0),Ps(ha,this,new ua(t))},[{key:"setProtectedHeader",value:function(e){return Cs(ha,this).setProtectedHeader(e),this}},{key:"sign",value:(e=Ds(Ns().m(function e(t,r){var n;return Ns().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,Cs(ha,this).sign(t,r);case 1:if(void 0!==(n=e.v).payload){e.n=2;break}throw TypeError("use the flattened module for creating JWS with b64: false");case 2:return e.a(2,"".concat(n.protected,".").concat(n.payload,".").concat(n.signature))}},e,this)})),function(t,r){return e.apply(this,arguments)})}]);var e}(),da=new WeakMap,fa=new WeakMap,ga=function(){return qs(function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Us(this,e),Es(this,da,void 0),Es(this,fa,void 0),Ps(fa,this,new na(t))},[{key:"setIssuer",value:function(e){return Cs(fa,this).iss=e,this}},{key:"setSubject",value:function(e){return Cs(fa,this).sub=e,this}},{key:"setAudience",value:function(e){return Cs(fa,this).aud=e,this}},{key:"setJti",value:function(e){return Cs(fa,this).jti=e,this}},{key:"setNotBefore",value:function(e){return Cs(fa,this).nbf=e,this}},{key:"setExpirationTime",value:function(e){return Cs(fa,this).exp=e,this}},{key:"setIssuedAt",value:function(e){return Cs(fa,this).iat=e,this}},{key:"setProtectedHeader",value:function(e){return Ps(da,this,e),this}},{key:"sign",value:(e=Ds(Ns().m(function e(t,r){var n,s;return Ns().w(function(e){for(;;)switch(e.n){case 0:if((s=new pa(Cs(fa,this).data())).setProtectedHeader(Cs(da,this)),!Array.isArray(null===(n=Cs(da,this))||void 0===n?void 0:n.crit)||!Cs(da,this).crit.includes("b64")||!1!==Cs(da,this).b64){e.n=1;break}throw new yi("JWTs MUST NOT use unencoded payload");case 1:return e.a(2,s.sign(t,r))}},e,this)})),function(t,r){return e.apply(this,arguments)})}]);var e}();function ma(e){for(var t="";t.length<e;)t+=Math.random().toString(36).substring(2);return t.substring(0,e)}function wa(e){return wa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wa(e)}function ya(){var e,t,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",s=r.toStringTag||"@@toStringTag";function i(r,n,s,i){var c=n&&n.prototype instanceof o?n:o,l=Object.create(c.prototype);return _a(l,"_invoke",function(r,n,s){var i,o,c,l=0,u=s||[],h=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,r){return i=t,o=0,c=e,p.n=r,a}};function d(r,n){for(o=r,c=n,t=0;!h&&l&&!s&&t<u.length;t++){var s,i=u[t],d=p.p,f=i[2];r>3?(s=f===n)&&(c=i[(o=i[4])?5:(o=3,3)],i[4]=i[5]=e):i[0]<=d&&((s=r<2&&d<i[1])?(o=0,p.v=n,p.n=i[1]):d<f&&(s=r<3||i[0]>n||n>f)&&(i[4]=r,i[5]=n,p.n=f,o=0))}if(s||r>1)return a;throw h=!0,n}return function(s,u,f){if(l>1)throw TypeError("Generator is already running");for(h&&1===u&&d(u,f),o=u,c=f;(t=o<2?e:c)||!h;){i||(o?o<3?(o>1&&(p.n=-1),d(o,c)):p.n=c:p.v=c);try{if(l=2,i){if(o||(s="next"),t=i[s]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,o<2&&(o=0)}else 1===o&&(t=i.return)&&t.call(i),o<2&&(c=TypeError("The iterator does not provide a '"+s+"' method"),o=1);i=e}else if((t=(h=p.n<0)?c:r.call(n,p))!==a)break}catch(t){i=e,o=1,c=t}finally{l=1}}return{value:t,done:h}}}(r,s,i),!0),l}var a={};function o(){}function c(){}function l(){}t=Object.getPrototypeOf;var u=[][n]?t(t([][n]())):(_a(t={},n,function(){return this}),t),h=l.prototype=o.prototype=Object.create(u);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,_a(e,s,"GeneratorFunction")),e.prototype=Object.create(h),e}return c.prototype=l,_a(h,"constructor",l),_a(l,"constructor",c),c.displayName="GeneratorFunction",_a(l,s,"GeneratorFunction"),_a(h),_a(h,s,"Generator"),_a(h,n,function(){return this}),_a(h,"toString",function(){return"[object Generator]"}),(ya=function(){return{w:i,m:p}})()}function _a(e,t,r,n){var s=Object.defineProperty;try{s({},"",{})}catch(e){s=0}_a=function(e,t,r,n){if(t)s?s(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n}):e[t]=r;else{var i=function(t,r){_a(e,t,function(e){return this._invoke(t,r,e)})};i("next",0),i("throw",1),i("return",2)}},_a(e,t,r,n)}function Sa(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function va(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sa(Object(r),!0).forEach(function(t){ba(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sa(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ba(e,t,r){return(t=function(e){var t=function(e){if("object"!=wa(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=wa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==wa(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xa(e,t,r,n,s,i,a){try{var o=e[i](a),c=o.value}catch(e){return void r(e)}o.done?t(c):Promise.resolve(c).then(n,s)}function ka(e){return function(){var t=this,r=arguments;return new Promise(function(n,s){var i=e.apply(t,r);function a(e){xa(i,n,s,a,o,"next",e)}function o(e){xa(i,n,s,a,o,"throw",e)}a(void 0)})}}function Aa(e){return Ea.apply(this,arguments)}function Ea(){return Ea=ka(ya().m(function e(t){var r,n,s,i,a,o=arguments;return ya().w(function(e){for(;;)switch(e.p=e.n){case 0:return n=o.length>2&&void 0!==o[2]?o[2]:null,s=t,i=r=o.length>1&&void 0!==o[1]?o[1]:{},t instanceof Request&&(s=t.url,i=va({method:t.method,headers:t.headers,body:t.body,mode:t.mode,credentials:t.credentials,cache:t.cache,redirect:t.redirect,referrer:t.referrer,integrity:t.integrity},r)),e.p=1,e.n=2,fetch(s,i);case 2:case 5:return e.a(2,e.v);case 3:return e.p=3,a=e.v,e.p=4,e.n=5,Ca(s,i,n);case 6:return e.p=6,e.v,e.a(2,{text:function(){return Promise.resolve("")},status:0,statusText:a.message||"Fetch Error",ok:!1})}},e,null,[[4,6],[1,3]])})),Ea.apply(this,arguments)}function Ca(e){return Pa.apply(this,arguments)}function Pa(){return Pa=ka(ya().m(function e(t){var r,n,s=arguments;return ya().w(function(e){for(;;)if(0===e.n)return r=s.length>1&&void 0!==s[1]?s[1]:{},n=s.length>2&&void 0!==s[2]?s[2]:null,e.a(2,new Promise(function(e,s){n?chrome.tabs.sendMessage(n,{greeting:"proxyRequest",url:t,options:r},function(t){if(chrome.runtime.lastError)s(new Error("Content script communication failed"));else if(t&&t.success){var r={text:function(){return Promise.resolve(t.text)},status:t.status,statusText:t.statusText,ok:t.status>=200&&t.status<300,headers:new Headers(t.headers||{})};e(r)}else s(new Error(t?t.error:"Proxy request failed"))}):chrome.tabs.query({active:!0,currentWindow:!0},function(n){if(0!==n.length){var i=n[0];chrome.tabs.sendMessage(i.id,{greeting:"proxyRequest",url:t,options:r},function(t){if(chrome.runtime.lastError)s(new Error("Content script communication failed"));else if(t&&t.success){var r={text:function(){return Promise.resolve(t.text)},status:t.status,statusText:t.statusText,ok:t.status>=200&&t.status<300,headers:new Headers(t.headers||{})};e(r)}else s(new Error(t?t.error:"Proxy request failed"))})}else s(new Error("No active tab found"))})}))},e)})),Pa.apply(this,arguments)}var Ia={},Ta=[".jpg",".png",".gif",".css",".svg",".ico",".js"],Oa=["ip","ip_port","domain","path","incomplete_path","url","sfz","mobile","mail","jwt","algorithm","secret"],Ra=["secret"],Na=[/["']?zopim[_-]?account[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?zhuliang[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?zensonatypepassword["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?zendesk[_-]?travis[_-]?github["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?server[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?partner[_-]?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?partner[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?account[_-]?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yt[_-]?account[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yangshun[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?yangshun[_-]?gh[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?www[_-]?googleapis[_-]?com["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpt[_-]?ssh[_-]?private[_-]?key[_-]?base64["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpt[_-]?ssh[_-]?connect["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpt[_-]?report[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpt[_-]?prepare[_-]?dir["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpt[_-]?db[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpt[_-]?db[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wporg[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wpjm[_-]?phpunit[_-]?google[_-]?geocode[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wordpress[_-]?db[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wordpress[_-]?db[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wincert[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?test[_-]?server["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?fb[_-]?password[_-]?3["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?fb[_-]?password[_-]?2["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?fb[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?basic[_-]?password[_-]?5["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?basic[_-]?password[_-]?4["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?basic[_-]?password[_-]?3["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?basic[_-]?password[_-]?2["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?widget[_-]?basic[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?watson[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?watson[_-]?device[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?watson[_-]?conversation[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?wakatime[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?vscetoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?visual[_-]?recognition[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?virustotal[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?vip[_-]?github[_-]?deploy[_-]?key[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?vip[_-]?github[_-]?deploy[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?vip[_-]?github[_-]?build[_-]?repo[_-]?deploy[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?v[_-]?sfdc[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?v[_-]?sfdc[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?usertravis["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?user[_-]?assets[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?user[_-]?assets[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?use[_-]?ssh["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?us[_-]?east[_-]?1[_-]?elb[_-]?amazonaws[_-]?com["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?urban[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?urban[_-]?master[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?urban[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?unity[_-]?serial["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?unity[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twitteroauthaccesstoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twitteroauthaccesssecret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twitter[_-]?consumer[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twitter[_-]?consumer[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twine[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twilio[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twilio[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twilio[_-]?configuration[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twilio[_-]?chat[_-]?account[_-]?api[_-]?service["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twilio[_-]?api[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?twilio[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?trex[_-]?okta[_-]?client[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?trex[_-]?client[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?secure[_-]?env[_-]?vars["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?pull[_-]?request["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?e2e[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?com[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?branch["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?travis[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?token[_-]?core[_-]?java["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?thera[_-]?oss[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?tester[_-]?keys[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?test[_-]?test["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?test[_-]?github[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?tesco[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?svn[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?surge[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?surge[_-]?login["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?stripe[_-]?public["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?stripe[_-]?private["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?strip[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?strip[_-]?publishable[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?stormpath[_-]?api[_-]?key[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?stormpath[_-]?api[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?starship[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?starship[_-]?account[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?star[_-]?test[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?star[_-]?test[_-]?location["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?star[_-]?test[_-]?bucket["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?star[_-]?test[_-]?aws[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?staging[_-]?base[_-]?url[_-]?runscope["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ssmtp[_-]?config["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sshpass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?srcclr[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?square[_-]?reader[_-]?sdk[_-]?repository[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sqssecretkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sqsaccesskey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?spring[_-]?mail[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?spotify[_-]?api[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?spotify[_-]?api[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?spaces[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?spaces[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?soundcloud[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?soundcloud[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatypepassword["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?token[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?token[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?nexus[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?gpg[_-]?passphrase["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonatype[_-]?gpg[_-]?key[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonar[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonar[_-]?project[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sonar[_-]?organization[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?socrata[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?socrata[_-]?app[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?snyk[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?snyk[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?snoowrap[_-]?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?snoowrap[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?snoowrap[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?slate[_-]?user[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?slash[_-]?developer[_-]?space[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?slash[_-]?developer[_-]?space["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?signing[_-]?key[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?signing[_-]?key[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?signing[_-]?key[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?signing[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?setsecretkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?setdstsecretkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?setdstaccesskey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ses[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ses[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?service[_-]?account[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sentry[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sentry[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sentry[_-]?endpoint["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sentry[_-]?default[_-]?org["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sentry[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendwithus[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendgrid[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendgrid[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendgrid[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendgrid[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendgrid[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sendgrid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?selion[_-]?selenium[_-]?host["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?selion[_-]?log[_-]?level[_-]?dev["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?segment[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secretid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secretkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secretaccesskey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?key[_-]?base["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?9["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?8["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?7["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?6["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?5["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?4["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?3["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?2["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?11["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?10["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?1["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?secret[_-]?0["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sdr[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?scrutinizer[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sauce[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sandbox[_-]?aws[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sandbox[_-]?aws[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sandbox[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?salesforce[_-]?bulk[_-]?test[_-]?security[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?salesforce[_-]?bulk[_-]?test[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sacloud[_-]?api["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sacloud[_-]?access[_-]?token[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?sacloud[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?user[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?secret[_-]?assets["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?secret[_-]?app[_-]?logs["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?key[_-]?assets["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?key[_-]?app[_-]?logs["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?external[_-]?3[_-]?amazonaws[_-]?com["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?bucket[_-]?name[_-]?assets["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?bucket[_-]?name[_-]?app[_-]?logs["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?s3[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rubygems[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rtd[_-]?store[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rtd[_-]?key[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?route53[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ropsten[_-]?private[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rinkeby[_-]?private[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rest[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?repotoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?reporting[_-]?webdav[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?reporting[_-]?webdav[_-]?pwd["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?release[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?release[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?registry[_-]?secure["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?registry[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rediscloud[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?redis[_-]?stunnel[_-]?urls["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?randrmusicapiaccesstoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?rabbitmq[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?quip[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?qiita[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?pypi[_-]?passowrd["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?pushover[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?publish[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?publish[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?publish[_-]?access["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?project[_-]?config["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?prod[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?prod[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?prod[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?private[_-]?signing[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?pring[_-]?mail[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?preferred[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?prebuild[_-]?auth["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?postgresql[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?postgresql[_-]?db["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?postgres[_-]?env[_-]?postgres[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?postgres[_-]?env[_-]?postgres[_-]?db["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?plugin[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?plotly[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?places[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?places[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?pg[_-]?host["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?pg[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?personal[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?personal[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?percy[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?percy[_-]?project["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?paypal[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?passwordtravis["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?parse[_-]?js[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?pagerduty[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?packagecloud[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ossrh[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ossrh[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ossrh[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ossrh[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ossrh[_-]?jira[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?os[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?os[_-]?auth[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?org[_-]?project[_-]?gradle[_-]?sonatype[_-]?nexus[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?org[_-]?gradle[_-]?project[_-]?sonatype[_-]?nexus[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?openwhisk[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?open[_-]?whisk[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?onesignal[_-]?user[_-]?auth[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?onesignal[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?omise[_-]?skey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?omise[_-]?pubkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?omise[_-]?pkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?omise[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?okta[_-]?oauth2[_-]?clientsecret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?okta[_-]?oauth2[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?okta[_-]?client[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ofta[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ofta[_-]?region["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ofta[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?octest[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?octest[_-]?app[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?octest[_-]?app[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?oc[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?object[_-]?store[_-]?creds["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?object[_-]?store[_-]?bucket["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?object[_-]?storage[_-]?region[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?object[_-]?storage[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?oauth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?numbers[_-]?service[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?nuget[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?nuget[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?nuget[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?npm[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?now[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?non[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?node[_-]?pre[_-]?gyp[_-]?secretaccesskey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?node[_-]?pre[_-]?gyp[_-]?github[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?node[_-]?pre[_-]?gyp[_-]?accesskeyid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?node[_-]?env["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ngrok[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ngrok[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?nexuspassword["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?nexus[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?new[_-]?relic[_-]?beta[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?netlify[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?nativeevents["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysqlsecret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysqlmasteruser["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysql[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysql[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysql[_-]?root[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysql[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysql[_-]?hostname["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mysql[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?my[_-]?secret[_-]?env["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?multi[_-]?workspace[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?multi[_-]?workflow[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?multi[_-]?disconnect[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?multi[_-]?connect[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?multi[_-]?bob[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?minio[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?minio[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mile[_-]?zero[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mh[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mh[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mg[_-]?public[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mg[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mapboxaccesstoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mapbox[_-]?aws[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mapbox[_-]?aws[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mapbox[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mapbox[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?manifest[_-]?app[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?manifest[_-]?app[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mandrill[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?managementapiaccesstoken["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?management[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?manage[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?manage[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?secret[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?pub[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?pub[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?priv[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailgun[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailer[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailchimp[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mailchimp[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?mail[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?magento[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?magento[_-]?auth[_-]?username ["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?magento[_-]?auth[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lottie[_-]?upload[_-]?cert[_-]?key[_-]?store[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lottie[_-]?upload[_-]?cert[_-]?key[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lottie[_-]?s3[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lottie[_-]?happo[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lottie[_-]?happo[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?looker[_-]?test[_-]?runner[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ll[_-]?shared[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ll[_-]?publish[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?linux[_-]?signing[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?linkedin[_-]?client[_-]?secretor lottie[_-]?s3[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lighthouse[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lektor[_-]?deploy[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?lektor[_-]?deploy[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?leanplum[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kxoltsn3vogdop92m["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kubeconfig["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kubecfg[_-]?s3[_-]?path["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kovan[_-]?private[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?keystore[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kafka[_-]?rest[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kafka[_-]?instance[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?kafka[_-]?admin[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?jwt[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?jdbc:mysql["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?jdbc[_-]?host["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?jdbc[_-]?databaseurl["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?itest[_-]?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ios[_-]?docs[_-]?deploy[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?internal[_-]?secrets["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?integration[_-]?test[_-]?appid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?integration[_-]?test[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?index[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ij[_-]?repo[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ij[_-]?repo[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?hub[_-]?dxia2[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?homebrew[_-]?github[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?hockeyapp[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?heroku[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?heroku[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?heroku[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?hb[_-]?codesign[_-]?key[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?hb[_-]?codesign[_-]?gpg[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?hab[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?hab[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?grgit[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gren[_-]?github[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gradle[_-]?signing[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gradle[_-]?signing[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gradle[_-]?publish[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gradle[_-]?publish[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gpg[_-]?secret[_-]?keys["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gpg[_-]?private[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gpg[_-]?passphrase["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gpg[_-]?ownertrust["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gpg[_-]?keyname["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gpg[_-]?key[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?google[_-]?private[_-]?key[_-]?(id)?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?google[_-]?maps[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?google[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?google[_-]?client[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?google[_-]?client[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?google[_-]?account[_-]?type["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gogs[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gitlab[_-]?user[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?tokens["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?repo["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?release[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?pwd["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?oauth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?oauth["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?hunter[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?hunter[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?deployment[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?deploy[_-]?hb[_-]?doc[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?auth["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?github[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?committer[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?committer[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?author[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?git[_-]?author[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ghost[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ghb[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?unstable[_-]?oauth[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?repo[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?oauth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?oauth[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?next[_-]?unstable[_-]?oauth[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?next[_-]?unstable[_-]?oauth[_-]?client[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?next[_-]?oauth[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gh[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gcs[_-]?bucket["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gcr[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gcloud[_-]?service[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gcloud[_-]?project["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?gcloud[_-]?bucket["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ftp[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ftp[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ftp[_-]?pw["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ftp[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ftp[_-]?login["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ftp[_-]?host["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?fossa[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?flickr[_-]?api[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?flickr[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?flask[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?firefox[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?firebase[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?firebase[_-]?project[_-]?develop["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?firebase[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?firebase[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?firebase[_-]?api[_-]?json["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?file[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?exp[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?eureka[_-]?awssecretkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?env[_-]?sonatype[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?env[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?env[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?env[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?env[_-]?heroku[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?env[_-]?github[_-]?oauth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?end[_-]?user[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?encryption[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?elasticsearch[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?elastic[_-]?cloud[_-]?auth["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?dsonar[_-]?projectkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?dsonar[_-]?login["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?droplet[_-]?travis[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?dropbox[_-]?oauth[_-]?bearer["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?doordash[_-]?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?dockerhubpassword["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?dockerhub[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?postgres[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?passwd["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?docker[_-]?hub[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?digitalocean[_-]?ssh[_-]?key[_-]?ids["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?digitalocean[_-]?ssh[_-]?key[_-]?body["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?digitalocean[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?dgpg[_-]?passphrase["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?deploy[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?deploy[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?deploy[_-]?secure["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?deploy[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ddgc[_-]?github[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ddg[_-]?test[_-]?email[_-]?pw["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ddg[_-]?test[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?pw["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?host["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?db[_-]?connection["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?datadog[_-]?app[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?datadog[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?database[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?database[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?database[_-]?port["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?database[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?database[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?database[_-]?host["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?danger[_-]?github[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cypress[_-]?record[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?coverity[_-]?scan[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?coveralls[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?coveralls[_-]?repo[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?coveralls[_-]?api[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cos[_-]?secrets["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?conversation[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?conversation[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?v2[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?test[_-]?org[_-]?cma[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?php[_-]?management[_-]?test[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?management[_-]?api[_-]?access[_-]?token[_-]?new["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?management[_-]?api[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?integration[_-]?management[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?cma[_-]?test[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?contentful[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?consumerkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?consumer[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?conekta[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?coding[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?codecov[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?codeclimate[_-]?repo[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?codacy[_-]?project[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cocoapods[_-]?trunk[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cocoapods[_-]?trunk[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cn[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cn[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?clu[_-]?ssh[_-]?private[_-]?key[_-]?base64["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?clu[_-]?repo[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudinary[_-]?url[_-]?staging["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudinary[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudflare[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudflare[_-]?auth[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudflare[_-]?auth[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudflare[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?service[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?processed[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?parsed[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?order[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?instance["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?audited[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloudant[_-]?archived[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cloud[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?clojars[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cli[_-]?e2e[_-]?cma[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?claimr[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?claimr[_-]?superuser["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?claimr[_-]?db["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?claimr[_-]?database["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ci[_-]?user[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ci[_-]?server[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ci[_-]?registry[_-]?user["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ci[_-]?project[_-]?url["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ci[_-]?deploy[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?chrome[_-]?refresh[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?chrome[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cheverny[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cf[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?certificate[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?censys[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cattle[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cattle[_-]?agent[_-]?instance[_-]?auth["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cattle[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cargo[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?cache[_-]?s3[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bx[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bx[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bundlesize[_-]?github[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?built[_-]?branch[_-]?deploy[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bucketeer[_-]?aws[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bucketeer[_-]?aws[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?browserstack[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?browser[_-]?stack[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?brackets[_-]?repo[_-]?oauth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?pwd["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?pass[_-]?prod["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?auth["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bluemix[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bintraykey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bintray[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bintray[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bintray[_-]?gpg[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bintray[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?bintray[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?b2[_-]?bucket["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?b2[_-]?app[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?awssecretkey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?awscn[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?awscn[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?awsaccesskeyid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?ses[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?ses[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?secrets["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?config[_-]?secretaccesskey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?config[_-]?accesskeyid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aws[_-]?access["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?author[_-]?npm[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?author[_-]?email[_-]?addr["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?auth0[_-]?client[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?auth0[_-]?api[_-]?clientsecret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?auth[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?assistant[_-]?iam[_-]?apikey["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?artifacts[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?artifacts[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?artifacts[_-]?bucket["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?artifacts[_-]?aws[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?artifacts[_-]?aws[_-]?access[_-]?key[_-]?id["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?artifactory[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?argos[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?apple[_-]?id[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?appclientsecret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?app[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?app[_-]?secrete["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?app[_-]?report[_-]?token[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?app[_-]?bucket[_-]?perm["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?apigw[_-]?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?apiary[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?api[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?api[_-]?key[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?api[_-]?key[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aos[_-]?sec["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?aos[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?ansible[_-]?vault[_-]?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?android[_-]?docs[_-]?deploy[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?anaconda[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?amazon[_-]?secret[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?amazon[_-]?bucket[_-]?name["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?alicloud[_-]?secret[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?alicloud[_-]?access[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?alias[_-]?pass["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?search[_-]?key[_-]?1["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?search[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?search[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?api[_-]?key[_-]?search["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?api[_-]?key[_-]?mcm["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?admin[_-]?key[_-]?mcm["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?admin[_-]?key[_-]?2["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?algolia[_-]?admin[_-]?key[_-]?1["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?air[-_]?table[-_]?api[-_]?key["']?[=:]["']?.+["']/gi,/["']?adzerk[_-]?api[_-]?key["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?admin[_-]?email["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?account[_-]?sid["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?access[_-]?token["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?access[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?access[_-]?key[_-]?secret["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?account["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?password["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?username["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[\w_-]*?password[\w_-]*?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[\w_-]*?username[\w_-]*?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[\w_-]*?accesskey[\w_-]*?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[\w_-]*?secret[\w_-]*?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[\w_-]*?bucket[\w_-]*?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[\w_-]*?token[\w_-]*?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?[-]+BEGIN \w+ PRIVATE KEY[-]+/gi,/["']?huawei\.oss\.(ak|sk|bucket\.name|endpoint|local\.path)["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?private[_-]?key[_-]?(id)?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/["']?account[_-]?(name|key)?["']?[^\S\r\n]*[=:][^\S\r\n]*["']?[\w-]+["']?/gi,/LTAI[A-Za-z\d]{12,30}/g,/AKID[A-Za-z\d]{13,40}/g,/JDC_[0-9A-Z]{25,40}/g,/["']?(?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16}["']?/g,/(?:AKLT|AKTP)[a-zA-Z0-9]{35,50}/g,/AKLT[a-zA-Z0-9-_]{16,28}/g,/AIza[0-9A-Za-z_\-]{35}/g,/[Bb]earer\s+[a-zA-Z0-9\-=._+/\\]{20,500}/g,/[Bb]asic\s+[A-Za-z0-9+/]{18,}={0,2}/g,/["''\[]*[Aa]uthorization["''\]]*\s*[:=]\s*[''"]?\b(?:[Tt]oken\s+)?[a-zA-Z0-9\-_+/]{20,500}[''"]?/g,/(glpat-[a-zA-Z0-9\-=_]{20,22})/g,/((?:ghp|gho|ghu|ghs|ghr|github_pat)_[a-zA-Z0-9_]{36,255})/g,/APID[a-zA-Z0-9]{32,42}/g,/["'](wx[a-z0-9]{15,18})["']/g,/["'](ww[a-z0-9]{15,18})["']/g,/["'](gh_[a-z0-9]{11,13})["']/g,/(?:admin_?pass|password|[a-z]{3,15}_?password|user_?pass|user_?pwd|admin_?pwd)\\?['"]*\s*[:=]\s*\\?['"][a-z0-9!@#$%&*]{5,20}\\?['"]/gi,/https:\/\/qyapi\.weixin\.qq\.com\/cgi\-bin\/webhook\/send\?key=[a-zA-Z0-9\-]{25,50}/gi,/https:\/\/oapi\.dingtalk\.com\/robot\/send\?access_token=[a-z0-9]{50,80}/gi,/https:\/\/open\.feishu\.cn\/open\-apis\/bot\/v2\/hook\/[a-z0-9\-]{25,50}/gi,/https:\/\/hooks\.slack\.com\/services\/[a-zA-Z0-9\-_]{6,12}\/[a-zA-Z0-9\-_]{6,12}\/[a-zA-Z0-9\-_]{15,24}/gi,/eyJrIjoi[a-zA-Z0-9\-_+/]{50,100}={0,2}/g,/glc_[A-Za-z0-9\-_+/]{32,200}={0,2}/g,/glsa_[A-Za-z0-9]{32}_[A-Fa-f0-9]{8}/g],ja={},La=-1;function Da(e){if("null"==e)return null;var t=[];return e.forEach(function(e,r,n){-1==t.indexOf(e)&&t.push(e)}),t}function Va(e,t){return e?t?(e.forEach(function(e,r,n){-1==t.indexOf(e)&&t.push(e)}),t):e:t}function Ma(e,t){var r=e.slice(0,e.length);return e.forEach(function(e,n,s){for(var i=0;i<Ta.length;i++)if(-1!=e.indexOf(Ta[i])){if(".js"==Ta[i]&&-1!=e.indexOf(".jsp"))continue;r.splice(r.indexOf(e),1),-1==t.indexOf(e)&&t.push(e)}}),{arr1:r,static:t}}function Ba(e){var t=[];return e.forEach(function(e,r,n){var s=0,i=0;(e.startsWith("'")||e.startsWith('"'))&&(s=1),(e.endsWith("'")||e.endsWith('"'))&&(i=1),t.push(e.substring(s,e.length-i))}),t}function Fa(e){var t={};return t.sfz=e.match(/['"]((\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)))['"]/g),t.mobile=e.match(/['"](1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7})['"]/g),t.mail=e.match(/['"][a-zA-Z0-9\._\-]*@[a-zA-Z0-9\._\-]{1,63}\.((?!js|css|jpg|jpeg|png|ico)[a-zA-Z]{2,})['"]/g),t.ip=e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(\/.*?)?['"]/g),t.ip_port=e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:\d{1,5}(\/.*?)?['"]/g),t.domain=e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?(\/)?['"]/g),t.path=e.match(/['"](?:\/|\.\.\/|\.\/)[^\/\>\< \)\(\{\}\,\'\"\\]([^\>\< \)\(\{\}\,\'\"\\])*?['"]/g),t.incomplete_path=e.match(/['"][^\/\>\< \)\(\{\}\,\'\"\\][\w\/]*?\/[\w\/]*?['"]/g),t.url=e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?(\/.*?)?['"]/g),t.jwt=e.match(/['"](ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,})['"]/g),t.algorithm=e.match(/\W(Base64\.encode|Base64\.decode|btoa|atob|CryptoJS\.AES|CryptoJS\.DES|JSEncrypt|rsa|KJUR|$\.md5|md5|sha1|sha256|sha512)[\(\.]/gi),t.secret=function(e){for(var t=[],r=Na.length-1;r>=0;r--){var n=e.match(Na[r]);if(null!=n)for(var s in n)t.push(n[s])}return t}(e),t.url&&t.url.map(function(e){t.ip=Va(t.ip,e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g)),t.ip_port=Va(t.ip_port,e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:\d{1,5}(\/.*?)?['"]/g)),t.domain=Va(t.domain,e.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?/g))}),t}var $a=new Headers;function Ua(){var e,t=ja[La],r=0;for(var n in Ia[t])if("done"!=n&&"tasklist"!=n&&"donetasklist"!=n&&"current"!=n&&"pretasknum"!=n){var s=Ia[t][n];"🈚️"!=s&&""!=s&&r++}chrome.action.setBadgeText({text:""+r}),Ia[t]&&Ia[t].donetasklist&&Ia[t].pretasknum&&Ia[t].donetasklist.length==Ia[t].pretasknum&&(Ia[t].done="done",chrome.storage.local.set(ba({},"findsomething_result_"+t,Ia[t]),function(){}),function(e){chrome.storage.local.get(["expire_index"],function(t){(t=t.expire_index)||(t={});var r=(new Date).toLocaleDateString("cn",{year:"numeric",month:"2-digit",day:"2-digit"}).replace(/\//g,"");t[e]=r,chrome.storage.local.set(ba({},"expire_index",t),function(){})})}(t),e=t,e=JSON.stringify(Ia[e]),chrome.storage.local.get(["webhook_setting"],function(t){if(t&&t.webhook_setting&&t.webhook_setting!={}&&null!=t.webhook_setting){var r={method:"GET",headers:$a,mode:"cors",cache:"default",credentials:"include"},n=new Headers;if(""!=t.webhook_setting.url){var s=t.webhook_setting.url;if("GET"==t.webhook_setting.method?s=s+"?"+t.webhook_setting.arg+"="+e:"POST"==t.webhook_setting.method?(n.append("Content-Type","application/json"),r.method="POST",""!=t.webhook_setting.arg?r.body=t.webhook_setting.arg+"="+e:r.body=e):console.log("webhook method error:"+t.webhook_setting.method),t.webhook_setting.headers!={})for(var i in t.webhook_setting.headers)n.append(i,t.webhook_setting.headers[i]);r.headers=n,Aa(new Request(s,r),r,null).then(function(e){}).catch(function(e){console.log("webhook fetch error",e)})}}}))}function Wa(e,t,r){for(var n=0;n<Oa.length;n++)if(null!=e[Oa[n]])if(Ra.indexOf(Oa[n])<0&&(e[Oa[n]]=Ba(e[Oa[n]])),e[Oa[n]].map(function(n){Ia[e.current].source[n]=t,Ia[e.current].ai[n]=r}),e.current in Ia&&null!=Ia[e.current][Oa[n]]){var s=Da(Va(Ia[e.current][Oa[n]],e[Oa[n]])).sort();if("static"in Ia[e.current])var i=Ma(s,Ia[e.current].static);else i=Ma(s,[]);Ia[e.current][Oa[n]]=i.arr1,Ia[e.current].static=i.static}else s=Da(e[Oa[n]]).sort(),i="static"in Ia[e.current]?Ma(s,Ia[e.current].static):Ma(s,[]),Ia[e.current].static=Da(i.static),Ia[e.current][Oa[n]]=Da(i.arr1)}$a.append("accept","*/*");var qa=function(e){var t=e.projectId,r=e.projectSecret,n=e.paywallAlias,s=e.customerIdGetter;n=n||"paywall_vip",s||(s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.prefix,r=e.storageKey;return t=t||"",r=r||"ezrevenueDeviceId",Ds(Ns().m(function e(){var n,s;return Ns().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,ti(ai).storage.local.get(r);case 1:if(s=r,!(n=e.v[s])){e.n=2;break}console.log("get deviceId ".concat(n)),e.n=3;break;case 2:return n=t+function(){var e=Date.now().toString(36),t=ma(22);return"".concat(e).concat(t)}(),console.log("create deviceId ".concat(n)),e.n=3,ti(ai).storage.local.set(Vs({},r,n));case 3:return e.a(2,n)}},e)}))}());var i=s,a={customerInfo:null,lastChecked:null};function o(e){return c.apply(this,arguments)}function c(){return c=Ds(Ns().m(function e(t){return Ns().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l(t);case 1:return e.a(2,e.v)}},e)})),c.apply(this,arguments)}function l(e){return u.apply(this,arguments)}function u(){return u=Ds(Ns().m(function e(t){var r,n;return Ns().w(function(e){for(;;)switch(e.n){case 0:if(null!=t&&t.refresh&&(a.customerInfo=null,a.lastChecked=null),a.customerInfo&&(r=a.lastChecked,n=Date.now()-r>18e5,(!r||n)&&(a.customerInfo=null,a.lastChecked=null)),a.customerInfo){e.n=2;break}return e.n=1,h();case 1:a.customerInfo=e.v,a.lastChecked=Date.now();case 2:return e.a(2,a.customerInfo)}},e)})),u.apply(this,arguments)}function h(){return p.apply(this,arguments)}function p(){return p=Ds(Ns().m(function e(){var s,a,o;return Ns().w(function(e){for(;;)switch(e.n){case 0:return s=function(e){var t=e.projectId,r=e.projectSecret,n={decodeToken:function(e){return Ds(Ns().m(function t(){var n,s,i;return Ns().w(function(t){for(;;)switch(t.n){case 0:return n=(new TextEncoder).encode(r),t.n=1,sa(e,n);case 1:return s=t.v,i=s.payload,t.a(2,i.result)}},t)}))()},encodeToken:function(e){return Ds(Ns().m(function n(){var s,i;return Ns().w(function(n){for(;;)switch(n.n){case 0:return e.exp=Date.now()+1800,e.nonce=ma(16),s=(new TextEncoder).encode(r),i=new ga(e).setProtectedHeader({alg:"HS256",project_id:t}),n.n=1,i.sign(s);case 1:return n.a(2,n.v)}},n)}))()},sendRequest:function(e){var t=e.url,r=e.content;return fetch(t,{method:"POST",headers:{"Content-Type":"text/plain"},body:r})},call:function(e,t){return Ds(Ns().m(function r(){var s,i,a,o,c,l,u;return Ns().w(function(r){for(;;)switch(r.p=r.n){case 0:return r.n=1,n.encodeToken({method:e,params:t});case 1:return s=r.v,r.p=2,r.n=3,n.sendRequest({url:"https://revenue.ezboti.com/api/v1/server/"+e,content:s});case 3:return i=r.v,r.n=4,i.text();case 4:return a=r.v,r.n=5,n.decodeToken(a);case 5:return r.a(2,r.v);case 6:throw r.p=6,(u=r.v).response&&(o=u.response,c=o.status,l=o.data,console.log("".concat(e," failed status=").concat(c,", body ==>"),l)),u;case 7:return r.a(2)}},r,null,[[2,6]])}))()}};return n}({projectId:t,projectSecret:r}),e.n=1,i();case 1:return a=e.v,e.n=2,s.call("customer.info",{paywall_alias:n,customer:{external_id:a},include_balance:!0});case 2:return o=e.v,e.a(2,(console.log("customerInfo",o),o))}},e)})),p.apply(this,arguments)}function d(){return d=Ds(Ns().m(function e(t){var r;return Ns().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,o();case 1:return r=e.v,e.n=2,f(r,t);case 2:return e.a(2,e.v)}},e)})),d.apply(this,arguments)}function f(e,t){return g.apply(this,arguments)}function g(){return g=Ds(Ns().m(function e(t,r){var n,s,i,a,c,l;return Ns().w(function(e){for(;;)switch(e.n){case 0:if(n=t.home_link.url){e.n=1;break}return e.a(2,null);case 1:return s=(null==r?void 0:r.screenWidth)||800,i=(null==r?void 0:r.screenHeight)||600,a=Math.min(s-32,800),c=Math.min(i-32,600),e.n=2,ti(ai).windows.create({url:n,type:"popup",width:a,height:c,left:Math.round((s-a)/2),top:Math.round((i-c)/2)});case 2:return l=e.v.id,console.log("Window created with ID: ".concat(l)),e.n=3,new Promise(function(e){var t=function(){var r=Ds(Ns().m(function r(n){var s,i;return Ns().w(function(r){for(;;)switch(r.n){case 0:if(n!==l){r.n=2;break}return ti(ai).windows.onRemoved.removeListener(t),s=e,r.n=1,o({refresh:!0});case 1:i=r.v,s(i);case 2:return r.a(2)}},r)}));return function(e){return r.apply(this,arguments)}}();ti(ai).windows.onRemoved.addListener(t)});case 3:return e.a(2,e.v)}},e)})),g.apply(this,arguments)}function m(){return m=Ds(Ns().m(function e(){var t,r,n,s,i=arguments;return Ns().w(function(e){for(;;)switch(e.n){case 0:return r=(t=i.length>0&&void 0!==i[0]?i[0]:{}).equityId,n=t.equityAlias,e.n=1,o();case 1:return s=e.v.balance_s.find(function(e){return r?e.equity.id===r:e.equity.alias===n||"equity_vip"}),e.a(2,null==s?void 0:s.is_balance_usable)}},e)})),m.apply(this,arguments)}var w={getCustomerId:i,getCustomerInfo:o,showPaywallPopup:function(e){return d.apply(this,arguments)},isBalanceUsable:function(){return m.apply(this,arguments)}},y={};return Object.keys(w).forEach(function(e){y["ezrevenue_".concat(e)]=w[e]}),ti(ai).runtime.onMessage.addListener(function(e){if(e.action){var t=y[e.action];return t?(console.log("ezrevenue request:",e),t(e.data)):void 0}}),Os({projectId:t,projectSecret:r,paywallAlias:n},w)}({projectId:"awpdt5n6ur0cq",projectSecret:"bczbcfunq0s323kyfgc1tw30rumf1gg4",paywallAlias:"paywall_vip"}),Ha={isVip:!1,lastCheck:0},Ka=3e5;function za(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return new Promise(function(t,r){chrome.storage.local.get(["dynamicFunctionAnalysis"],function(n){if(!0===n.dynamicFunctionAnalysis){var s=Date.now();if(!e&&s-Ha.lastCheck<Ka)return void t(Ha.isVip);qa.isBalanceUsable().then(function(e){Ha.isVip=e,Ha.lastCheck=s,t(e)}).catch(r)}else Ha.isVip=!1,Ha.lastCheck=0,t(!1)})})}setInterval(function(){za(!0)},Ka),chrome.runtime.onMessage.addListener(function(e,t,r){var n=new AbortController,s=new Headers;s.append("accept","*/*");var i={method:"GET",headers:s,mode:"cors",cache:"default",credentials:"include",signal:n.signal};if(["find"].includes(e.greeting)){var a=Boolean(e.ai);e.current in Ia?(Ia[e.current].done="",Ia[e.current].tasklist=[],Ia[e.current].donetasklist=[]):Ia[e.current]={current:e.current,tasklist:[],donetasklist:[],source:{},showCode:[],ai:{}};var o=Fa(e.source);o.current=e.current,o.static=null,console.log(o),Wa(o,e.current,a),chrome.storage.local.set(ba({},"findsomething_result_"+e.current,Ia[e.current]),function(){}),ja[t.tab.id]=e.current,Ua();var c=[];return Ia[e.current].pretasknum=e.data.length,new Set,za().then(function(r){e.data.map(function(n){try{if(Ia[e.current].tasklist.push(0),n==e.current)return void Ia[e.current].donetasklist.push(0);var s=Aa(new Request(n,i),i,t.tab.id).then(function(s){s.text().then(function(s){var i=s;r&&function(e){return g.apply(this,arguments)}(s).then(function(t){t.error||null!=t.suspiciousCodes&&t.suspiciousCodes.size>0&&(t.suspiciousCodes.forEach(function(t){Ia[e.current].showCode.includes(t)||(Ia[e.current].showCode.push(t),Ia[e.current].source[t]=n)}),chrome.storage.local.set(ba({},"findsomething_result_"+e.current,Ia[e.current]),function(){}))}),(i=Fa(i)).current=e.current,Wa(i,n,a),Ia[e.current].donetasklist.push(0),chrome.storage.local.set(ba({},"findsomething_result_"+e.current,Ia[e.current]),function(){}),ja[t.tab.id]=e.current,Ua()})}).catch(function(t){console.log("fetch error",t.name,t.message,t.stack,n),Ia[e.current].donetasklist.push(0),Ua(),chrome.storage.local.set(ba({},"findsomething_result_"+e.current,Ia[e.current]),function(){})});c.push(s)}catch(t){Ia[e.current].donetasklist.push(0)}})}),chrome.storage.local.get(["fetch_timeout"],function(t){if(1==t.fetch_timeout){var r=new Promise(function(e,t){setTimeout(function(){e(new Response("findsomething fetch timeout",{status:504,statusText:"timeout"})),n.abort()},2e3)});c.push(r),Promise.race(c).then(function(){Ua(),chrome.storage.local.set(ba({},"findsomething_result_"+e.current,Ia[e.current]),function(){})}).catch(function(e){console.log(e),n=null})}else Promise.all(c).then(function(){Ua(),chrome.storage.local.set(ba({},"findsomething_result_"+e.current,Ia[e.current]),function(){})})}),!0}if("get"==e.greeting)return r(Ia[e.current]),!0;if("AIcode"==e.greeting){var l=e.code,u=e.current,h=e.from,p=e.tabid;return console.log("background: AIcode",e,l,u,h),chrome.tabs.sendMessage(p,{greeting:"toast",message:"开始AI代码分析..."}),chrome.action.setBadgeText({text:"1/3"}),chrome.action.setBadgeBackgroundColor({color:"#4285f4"}),function(e,t){return As.apply(this,arguments)}(l,h).then(function(e){console.log(e),chrome.action.setBadgeText({text:"2/3"});var t=e.split("\n").filter(function(e){return""!==e.trim()});if(t.length<2)return console.log("错误：响应内容至少需要两行数据"),chrome.action.setBadgeText({text:"✗"}),chrome.action.setBadgeBackgroundColor({color:"#ea4335"}),void chrome.tabs.sendMessage(p,{greeting:"toast",message:e});var r=t[t.length-1].trim().toLowerCase();if("ok"!==r)return"error"===r?(console.log("处理失败：服务返回错误状态"),chrome.action.setBadgeText({text:"✗"}),chrome.action.setBadgeBackgroundColor({color:"#ea4335"}),void chrome.tabs.sendMessage(p,{greeting:"toast",message:"AI分析失败："+e})):(console.log("错误：未识别的状态行 -",r),chrome.action.setBadgeText({text:"✗"}),chrome.action.setBadgeBackgroundColor({color:"#ea4335"}),void chrome.tabs.sendMessage(p,{greeting:"toast",message:"AI分析异常："+e}));var n=t[t.length-2].trim(),s=t.slice(0,-2).map(function(e){return e.trim()}),i=n.endsWith("/")?n:n+"/",a=s.map(function(e){var t=e.startsWith("/")?e.substring(1):e;return i+t});console.log(a),chrome.action.setBadgeText({text:"✓"}),chrome.action.setBadgeBackgroundColor({color:"#34a853"}),chrome.tabs.sendMessage(p,{greeting:"toast",message:"AI分析成功！发现 ".concat(s.length," 个可疑文件，将继续分析...")}),chrome.tabs.sendMessage(p,{greeting:"re-find",current:u,target_list:a})}).catch(function(e){console.log("AIcode执行失败:",e),chrome.action.setBadgeText({text:"✗"}),chrome.action.setBadgeBackgroundColor({color:"#ea4335"}),chrome.tabs.sendMessage(p,{greeting:"toast",message:"AI分析执行失败："+e.message})}),!0}"refreshVipStatus"===e.greeting&&za(!0)}),chrome.tabs.onUpdated.addListener(function(e,t){"complete"==t.status&&e==La&&Ua()}),chrome.tabs.onActivated.addListener(function(e){La=e.tabId,Ua()}),chrome.tabs.query({active:!0,currentWindow:!0},function(e){e&&e[0]&&(La=e[0].id,Ua())})})()})();