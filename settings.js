// AI默认配置（仅js变量，不自动写入页面，便于上线前统一修改）
const AI_DEFAULT_CONFIG = {
    AIbaseURL: "https://ark.cn-beijing.volces.com/api/v3/chat",
  AIapiKey: "",
  AImodel: "doubao-1-5-lite-32k-250115",
  AISystemPrompt: `你将扮演一个js解释器，我将给你一段js代码和一个url，这段代码应该是用来动态生成js路径的，你需要帮我还原这段代码，给出全部javascript文件的url路径，路径需要以http开头，以.js结尾。
  假设需要还原的js路径与给出的url同目录，根据还原的js示例，推理出js所在的相对路径。
  我将给你一个示例：
  输入：function(e){return n.p+""+{6:"a0c1a83"}[e]+".js"} 和 http://www.example.com/artifact/artifact-xxx/1750771666/04695da.js
  输出：根据代码还原出的js路径是a0c1a83.js，根据给到的url，推测js路径在url中属于最后一部分04695da.js的位置，那么相对路径是http://www.example.com/artifact/artifact-xxx/1750771666/。
  注意输出格式有以下要求：
  1、先输出还原出来的路径，然后输出相对路径，最后一行输出ok或error表示还原成功或无法还原。
  2、输出路径时每行一条。
  3、不要输出js路径、相对路径和还原状态之外的任何内容，如果代码不是用来生成js路径的，那么仅输出error。`,
  AIUserPrompt: "将这段动态加载代码还原为Js路径，输出结果一行一个。给你的url：{url}。"
};

// 在浏览器中打开使用手册
function openManual() {
    chrome.tabs.create({
        url: chrome.runtime.getURL('getstarted.html')
    });
}

function setTextContentById(id){
    document.getElementById(id).textContent = chrome.i18n.getMessage(id);
}

function init_locales() {
    const settingIdList = [
        "Zhuye",
        "Peizhi",
		"settingClearCache",
		"settingClearLocalStorage",
		"settingGlobalFloatingWindow",
		"settingAutoTimeout",
		"settingWebhook",
		"settingWebhookUrl",
		"settingWebhookMethod",
		"settingWebhookArg",
		"settingWebhookHeaders",
		"settingDomainAllowList",
		"settingSafe",
		"settingDynamicFunctionAnalysis",
		"settingAI",
		"settingAIbaseURL",
		"settingAIapiKey",
		"settingAImodel",
		"settingAISystemPrompt",
		"settingAIUserPrompt",
		"global_float",
		"fetch_timeout",
		"settingSafeMode",
		"dynamicFunctionAnalysis"
    ];

    for (const id of settingIdList) {
    	try{
    		setTextContentById(id)
    	}catch{
    		console.log(id)
    	}
        
    }

    const settingResetAndSaveList = document.getElementsByClassName("settingResetAndSave");
    for (const settingResetAndSave of settingResetAndSaveList) {
    	settingResetAndSave.textContent = chrome.i18n.getMessage("settingResetAndSave");
    }
    const settingSaveList = document.getElementsByClassName("settingSave");
    for (const settingSave of settingSaveList) {
    	settingSave.textContent = chrome.i18n.getMessage("settingSave");
    }
    document.getElementById("allowlist").placeholder = chrome.i18n.getMessage("settingDomainAllowListTip");
}

init_locales()

// 添加使用手册点击事件
document.addEventListener('DOMContentLoaded', function() {
    const manualLink = document.getElementById('manual-link');
    if (manualLink) {
        manualLink.addEventListener('click', function(e) {
            e.preventDefault();
            openManual();
        });
    }
});

document.getElementById("save").onclick=function () {
	let webhook_setting = {};
	webhook_setting['url'] = document.getElementById('url').value;
	webhook_setting['method'] = document.getElementById('method').value;
	webhook_setting['arg'] = document.getElementById('arg').value;
	webhook_setting['headers'] = JSON.parse(document.getElementById('headers').value);
	// console.log(webhook_setting);
	chrome.storage.local.set({"webhook_setting": webhook_setting});
}
document.getElementById("reset").onclick=function () {
	let webhook_setting = {"url":"","arg":"","headers":{}};
	document.getElementById('url').value = "";
	document.getElementById('arg').value = "";
	document.getElementById('headers').value = "{}";
	// console.log(webhook_setting);
	chrome.storage.local.set({"webhook_setting": webhook_setting});
}

document.getElementById("save_allowlist").onclick=function () {
	snsArr = document.getElementById('allowlist').value.split(/[(\r\n)\r\n]+/);
	snsArr.forEach((item, index)=>{
		if(!item){
			snsArr.splice(index,1);
		}
	})
	// console.log(snsArr)
	chrome.storage.local.set({"allowlist": snsArr});
}
document.getElementById("reset_allowlist").onclick=function () {
	document.getElementById('allowlist').value = "";
	chrome.storage.local.set({"allowlist": []});
}
document.getElementById("save_AI").onclick=function () {
	let AI_setting = {
		"AIbaseURL": document.getElementById('AIbaseURL').value,
		"AIapiKey":document.getElementById('AIapiKey').value,
		"AImodel": document.getElementById('AImodel').value,
		"AISystemPrompt": document.getElementById('AISystemPrompt').value,
		"AIUserPrompt": document.getElementById('AIUserPrompt').value
	};
	chrome.storage.sync.set({"AI": AI_setting});
}
document.getElementById("reset_AI").onclick=function () {
	document.getElementById('AIbaseURL').value = "";
	document.getElementById('AIapiKey').value = "";
	document.getElementById('AImodel').value = "";
	document.getElementById('AISystemPrompt').value = "";
	document.getElementById('AIUserPrompt').value = "";
	chrome.storage.sync.set({"AI": {"AIbaseURL": "","AIapiKey":"","AImodel":"","AISystemPrompt":"","AIUserPrompt":""}});
}

document.getElementById("settingClearLocalStorage").onclick=function () {
	// chrome.storage.local.clear();
	// 获取所有存储的数据
chrome.storage.local.get(null, function(items) {
    // 过滤出以 findsomething_result 开头的键
    const resultKeys = Object.keys(items).filter(key => 
			key.startsWith('findsomething_result')
		);
		// 删除这些键
		chrome.storage.local.remove(resultKeys);
	});
	console.log(chrome.i18n.getMessage("settingClearComplete"));
	alert(chrome.i18n.getMessage("settingClearComplete"));
}

document.getElementById("global_float").onclick=function () {
	// var webhook_setting = {};
	chrome.storage.local.get(["global_float"], function(settings){
		// console.log(settings);
		chrome.storage.local.set({"global_float": settings["global_float"]==true ? false : true});
		document.getElementById('global_float').textContent = settings["global_float"]==true ? chrome.i18n.getMessage("settingClosed") : chrome.i18n.getMessage("settingOpened");
	});
	// console.log(webhook_setting);
}

document.getElementById("fetch_timeout").onclick=function () {
	// var webhook_setting = {};
	chrome.storage.local.get(["fetch_timeout"], function(settings){
		// console.log(settings);
		chrome.storage.local.set({"fetch_timeout": settings["fetch_timeout"]==true ? false : true});
		document.getElementById('fetch_timeout').textContent = settings["fetch_timeout"]==true ? chrome.i18n.getMessage("settingClosed") : chrome.i18n.getMessage("settingOpened");
	});
	// console.log(webhook_setting);
}

document.getElementById("settingSafeMode").onclick=function () {
	// var webhook_setting = {};
	chrome.storage.local.get(["settingSafeMode"], function(settings){
		// console.log(settings);
		chrome.storage.local.set({"settingSafeMode": settings["settingSafeMode"]==true ? false : true});
		document.getElementById('settingSafeMode').textContent = settings["settingSafeMode"]==true ? chrome.i18n.getMessage("settingClosed") : chrome.i18n.getMessage("settingOpened");
	});
}

document.getElementById("dynamicFunctionAnalysis").onclick=function () {
	// var webhook_setting = {};
	chrome.storage.local.get(["dynamicFunctionAnalysis"], function(settings){
		// console.log(settings);
		chrome.storage.local.set({"dynamicFunctionAnalysis": settings["dynamicFunctionAnalysis"]==true ? false : true});
		document.getElementById('dynamicFunctionAnalysis').textContent = settings["dynamicFunctionAnalysis"]==true ? chrome.i18n.getMessage("settingClosed") : chrome.i18n.getMessage("settingOpened");
	});
}


chrome.storage.local.get(["webhook_setting"], function(settings){
	// console.log(settings);
	if(!settings || settings == {} || !settings["webhook_setting"] ){
        console.log('获取webhook_setting失败');
        return;
    }
	document.getElementById('url').value = settings["webhook_setting"]['url'];
	document.getElementById('method').value = settings["webhook_setting"]['method'];
	document.getElementById('arg').value = settings["webhook_setting"]['arg'];
	document.getElementById('headers').value = JSON.stringify(settings["webhook_setting"]['headers']);
});
chrome.storage.local.get(["global_float"], function(settings){
	document.getElementById('global_float').textContent = settings["global_float"]==true ? chrome.i18n.getMessage("settingOpened") : chrome.i18n.getMessage("settingClosed");
});
chrome.storage.local.get(["fetch_timeout"], function(settings){
	document.getElementById('fetch_timeout').textContent = settings["fetch_timeout"]==true ? chrome.i18n.getMessage("settingOpened") : chrome.i18n.getMessage("settingClosed");
});

chrome.storage.local.get(["settingSafeMode"], function(settings){
	document.getElementById('settingSafeMode').textContent = settings["settingSafeMode"]==true ? chrome.i18n.getMessage("settingOpened") : chrome.i18n.getMessage("settingClosed");
	if(settings["settingSafeMode"]==null){
		chrome.storage.local.set({"settingSafeMode": true});
		document.getElementById('settingSafeMode').textContent = chrome.i18n.getMessage("settingOpened");
	}
});

chrome.storage.local.get(["dynamicFunctionAnalysis"], function(settings){
	document.getElementById('dynamicFunctionAnalysis').textContent = settings["dynamicFunctionAnalysis"]==true ? chrome.i18n.getMessage("settingOpened") : chrome.i18n.getMessage("settingClosed");
	if(settings["dynamicFunctionAnalysis"]==null){
		chrome.storage.local.set({"dynamicFunctionAnalysis": true});
		document.getElementById('dynamicFunctionAnalysis').textContent = chrome.i18n.getMessage("settingOpened");
	}
});
chrome.storage.local.get(["allowlist"], function(allowlist){
	if(allowlist && allowlist["allowlist"]){
		document.getElementById('allowlist').textContent = allowlist["allowlist"].join('\n');
	}
});
chrome.storage.sync.get(["AI"], function(settings){
	if(settings && settings["AI"]){
		document.getElementById('AIbaseURL').value = settings["AI"]["AIbaseURL"];
		document.getElementById('AIapiKey').value = settings["AI"]["AIapiKey"];
		document.getElementById('AImodel').value = settings["AI"]["AImodel"];
		document.getElementById('AISystemPrompt').value = settings["AI"]["AISystemPrompt"] || AI_DEFAULT_CONFIG["AISystemPrompt"];
		document.getElementById('AIUserPrompt').value = settings["AI"]["AIUserPrompt"] || AI_DEFAULT_CONFIG["AIUserPrompt"];
	}
	else{
		document.getElementById('AIbaseURL').value = AI_DEFAULT_CONFIG["AIbaseURL"];
		document.getElementById('AIapiKey').value = AI_DEFAULT_CONFIG["AIapiKey"];
		document.getElementById('AImodel').value = AI_DEFAULT_CONFIG["AImodel"];
		document.getElementById('AISystemPrompt').value = AI_DEFAULT_CONFIG["AISystemPrompt"];
		document.getElementById('AIUserPrompt').value = AI_DEFAULT_CONFIG["AIUserPrompt"];
	}
});
