{"Peizhi": {"description": "Settings page of the popup", "message": "Settings"}, "Zhuye": {"description": "Home page of the popup", "message": "Home"}, "extDescription": {"description": "Extension description", "message": "Find interesting things in the webpage's source code or JavaScript"}, "extName": {"description": "Extension name", "message": "FindSomething"}, "getstartedContent": {"description": "getstarted", "message": "How can you use it well if you don't understand the features?\n## Home Page Features\n* Field matching and display for IP, IP+port, domain, ID card number, phone number, email, JWT, encryption algorithms, sensitive information, path, incomplete path, full URL, static path, etc.\n\n* Dynamic Code Analysis and AI Restoration\n    * Detect and analyze dynamically loaded code in JavaScript, including dynamic loading code generated by webpack bundling tools.\n    * By clicking the black < before functions suspected of dynamically generating JS files, you can use the integrated OpenAI API to analyze and restore the dynamic code, ultimately obtaining all JS files of the entire project. This means the plugin can perform information gathering on the entire project, bypassing frontend trigger logic and permission validation. Currently verified to work with functions in the form of function(e){return a.p+\"\"+{4:\"679e89\"}[e]+\".js\"}.\n    * Once you click the black < for dynamic analysis, the plugin icon will display the AI request progress, such as 1/3.\n    * AI analysis may be slow, requiring approximately 30 seconds of waiting.\n    * The scale of JS files restored by AI will be indicated through toast notifications.\n    * JS files obtained through AI restoration will be searched for information again, and new content will be displayed with black <.\n    * In the settings page, you can customize AI API address, key, model, and prompts. Verified models include doubao-1-5-lite-32k-250115 and deepseek-chat.\n    * This feature will be paid as a membership. Membership status is bound to device ID and stored in chrome.storage, so be careful not to clear this information. You can try to crack it, it's not difficult, but please don't spread the cracking method.\n\n* Copy\n    * A copy button for each area, which can copy the content of the corresponding area\n    * A separate \"Copy URL\" button that can copy the content of the PATH area and prepend the current page's URL to each line to form a complete URL.\n\n* Information Source\n    * Each displayed information is preceded by an orange '<'. Hover your mouse over it and stay silent for 2 seconds to see the source URL of the current information. You can also open the source link by \"ctrl+click\" or \"right-click - open link in new tab\".\n\n* Progress Display\n    * The processing progress is displayed under the \"Home\" page, e.g., Processing: 1/20, where 1 is the number of links requested and 20 is the total number of links to be requested. If all requests are completed, it will display as Completed: 20/20.\n    * The entire page's refresh logic is also based on processing progress, refreshing data by listening for data changes before processing is complete.\n\n## Configuration Page Features\n\n* Clear Cache\n    * Since the extracted data is stored in the browser's localstorage, it can occupy memory if not cleared for a long time.\n    * The plugin introduced an automatic expiration logic in version 2.0.17, where data not accessed within 7 days will expire.\n    * Users can also manually click \"Clear\" to immediately clear the stored data.\n\n* Global Floating Window\n    * Once opened, the FindSomething page will embed into every opened page, eliminating the need to interact with the plugin.\n    * However, new features have not yet been added to the global floating window, only maintaining basic functionality.\n\n* Auto Timeout\n    * This setting is off by default. When enabled, requests initiated by the plugin backend will be terminated after 2s to avoid long waiting times.\n\n* Safe Mode\n    * In safe mode, the plugin will only access JavaScript resources to avoid leaving the current page or accessing sensitive operation links.\n    * When safe mode is turned off, it will access all URLs of the current page's src and href attributes, regardless of link type, including CSS, images, or APIs, which may lead to unexpected requests.\n    * Safe mode is enabled by default.\n\n* Dynamic Code Analysis\n    * Controls the switch for dynamic code analysis feature. When turned off, it won't check membership status or perform dynamic code analysis.\n\n* Webhook Callback\n    * I recommend using POST, as GET has limitations on the length of parameters.\n    * If the parameter name is written in the request parameter location, then the request body will be in the format data={\"xx\":\"xx\"}.\n    * If no parameter name is written in the request parameter location, then the request body will be in JSON format.\n    * The request body includes configuration, progress, all extracted information, and their sources.\n    * Some websites extract a lot of content, so be mindful of configuring your webhook site to handle such a large amount of data in one request.\n\n* Domain Allowlist\n    * The logic is based on ending with xxx. Domains on the allowlist will not be requested.\n\n* AI Configuration\n    * You can customize the API address (write to the chat directory, such as https://api.deepseek.com/chat/ or https://ark.cn-beijing.volces.com/api/v3/chat), key, model (verified models include doubao-1-5-lite-32k-250115 and deepseek-chat), system prompt, and user prompt. The {url} placeholder in the user prompt represents the current JS path, used for AI to infer relative paths."}, "popupAIAnalysis": {"description": "AI analysis prompt on popup page", "message": "Click for AI Analysis"}, "popupAlgorithm": {"description": "Title on the popup page: Algorithm", "message": "Algorithm"}, "popupAnalyze": {"description": "Title on popup page: Dynamic Code Analysis", "message": "Dynamic Code Analysis"}, "popupClickToCollapse": {"description": "Click to collapse text prompt", "message": "Click to collapse"}, "popupClickToExpand": {"description": "Click to expand text prompt", "message": "Click to expand"}, "popupComplete": {"description": "Progress prompt on the popup page: Processing Complete", "message": "Processing Complete:"}, "popupCopy": {"description": "Copy button on the popup page", "message": "Copy"}, "popupCopyurl": {"description": "Copy URL button on the popup page", "message": "Copy URL"}, "popupDomain": {"description": "Title on the popup page: Domain", "message": "Domain"}, "popupIncompletePath": {"description": "Title on the popup page: Incomplete Path", "message": "Incomplete Path"}, "popupIp": {"description": "Title on the popup page: IP", "message": "IP"}, "popupIpPort": {"description": "Title on the popup page: IP_PORT", "message": "IP and Port"}, "popupJwt": {"description": "Title on the popup page: JWT", "message": "JWT"}, "popupMail": {"description": "Title on the popup page: <PERSON><PERSON>", "message": "Email"}, "popupMobile": {"description": "Title on the popup page: Mobile Number", "message": "Mobile Number"}, "popupPath": {"description": "Title on the popup page: Path", "message": "PATH"}, "popupProcessing": {"description": "Progress prompt on the popup page: Processing", "message": "Processing..."}, "popupSecret": {"description": "Title on the popup page: Sensitive Information", "message": "Sensitive Information"}, "popupSfz": {"description": "Title on the popup page: ID Card", "message": "ID Card"}, "popupStaticPath": {"description": "Title on the popup page: Static Path", "message": "Static Path"}, "popupTipClickBeforeCopy": {"description": "Copy tip on the popup page when an error occurs", "message": "Please click on the original page before copying :)"}, "popupUrl": {"description": "Title on the popup page: URL", "message": "URL"}, "popupVipFeature": {"description": "VIP feature prompt on popup page", "message": "VIP Feature"}, "popupVipMember": {"description": "VIP button on popup page: My VIP", "message": "My VIP"}, "popupVipSubscribe": {"description": "VIP button on popup page: Subscribe VIP", "message": "Subscribe VIP"}, "popupVipSwitchOff": {"description": "Button text when VIP switch is off on popup page", "message": "Please Turn On VIP Switch"}, "popupVipSwitchOffAlert": {"description": "Alert when clicking button with VIP switch off on popup page", "message": "Please turn on VIP switch in settings page first"}, "settingAI": {"description": "Title on the settings page: AI Configuration", "message": "AI Configuration"}, "settingAISystemPrompt": {"description": "Setting item on the settings page: AI System Prompt", "message": "System Prompt"}, "settingAIUserPrompt": {"description": "Setting item on the settings page: AI User Prompt", "message": "User Prompt"}, "settingAIapiKey": {"description": "Setting item on the settings page: AI API Key", "message": "API Key"}, "settingAIbaseURL": {"description": "Setting item on the settings page: AI API URL", "message": "API URL"}, "settingAImodel": {"description": "Setting item on the settings page: AI Model", "message": "Model"}, "settingAutoTimeout": {"description": "Title on the settings page: Auto Timeout", "message": "Auto Timeout"}, "settingClearCache": {"description": "Title on the settings page: Clear Cache", "message": "<PERSON>ache"}, "settingClearComplete": {"description": "Prompt on the settings page: Clear Complete", "message": "Clear Complete"}, "settingClearLocalStorage": {"description": "But<PERSON> on the settings page: Clear", "message": "Clear"}, "settingClosed": {"description": "<PERSON><PERSON> on the settings page: Closed", "message": "Closed"}, "settingDomainAllowList": {"description": "Setting item on the settings page: Domain Allowlist", "message": "Domain Allowlist"}, "settingDomainAllowListTip": {"description": "Tip in the settings page for Domain Allowlist", "message": "Enter the end part of the domain, separated by new lines, if not configured defaults to .google.com, .amazon.com, portswigger.net"}, "settingDynamicFunctionAnalysis": {"description": "When the VIP switch is on, only VIP users can use the feature. When off, no one can use it.", "message": "VIP Switch"}, "settingGlobalFloatingWindow": {"description": "Title on the settings page: Global Floating Window", "message": "Global Floating Window"}, "settingOpened": {"description": "Button on the settings page: Opened", "message": "Opened"}, "settingResetAndSave": {"description": "But<PERSON> on the settings page: Reset and Save", "message": "Reset and Save"}, "settingSafe": {"description": "Title on the settings page: Safe Mode", "message": "Safe Mode"}, "settingSave": {"description": "<PERSON><PERSON> on the settings page: Save", "message": "Save"}, "settingWebhook": {"description": "Title on the settings page: Webhook", "message": "Webhook"}, "settingWebhookArg": {"description": "Setting item on the settings page: Request Parameters", "message": "Request Parameters"}, "settingWebhookHeaders": {"description": "Setting item on the settings page: Custom Headers", "message": "Custom Headers"}, "settingWebhookMethod": {"description": "Setting item on the settings page: Request Method", "message": "Request Method"}, "settingWebhookUrl": {"description": "Setting item on the settings page: Callback URL", "message": "Callback URL"}}