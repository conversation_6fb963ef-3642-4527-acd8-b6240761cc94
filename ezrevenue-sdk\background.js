let e;function t(e){return e&&e.__esModule?e.default:e}var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},a={},s={},n=r.parcelRequiref725;null==n&&((n=function(e){if(e in a)return a[e].exports;if(e in s){var t=s[e];delete s[e];var r={id:e,exports:{}};return a[e]=r,t.call(r.exports,r,r.exports),r.exports}var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n}).register=function(e,t){s[e]=t},r.parcelRequiref725=n),(0,n.register)("3OecN",function(e,t){var r,a;"undefined"!=typeof globalThis||"undefined"!=typeof self&&self,a=function(e){if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw Error("This script should only be loaded in a browser extension.");globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id?e.exports=globalThis.browser:e.exports=(e=>{let t={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(t).length)throw Error("api-metadata.json has not been included in browser-polyfill");class r extends WeakMap{constructor(e,t){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}let a=e=>e&&"object"==typeof e&&"function"==typeof e.then,s=(t,r)=>(...a)=>{e.runtime.lastError?t.reject(Error(e.runtime.lastError.message)):r.singleCallbackArg||a.length<=1&&!1!==r.singleCallbackArg?t.resolve(a[0]):t.resolve(a)},n=e=>1==e?"argument":"arguments",i=(e,t)=>function(r,...a){if(a.length<t.minArgs)throw Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise((n,i)=>{if(t.fallbackToNoCallback)try{r[e](...a,s({resolve:n,reject:i},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...a),t.fallbackToNoCallback=!1,t.noCallback=!0,n()}else t.noCallback?(r[e](...a),n()):r[e](...a,s({resolve:n,reject:i},t))})},o=(e,t,r)=>new Proxy(t,{apply:(t,a,s)=>r.call(a,e,...s)}),c=Function.call.bind(Object.prototype.hasOwnProperty),l=(e,t={},r={})=>{let a=Object.create(null);return new Proxy(Object.create(e),{has:(t,r)=>r in e||r in a,get(s,n,g){if(n in a)return a[n];if(!(n in e))return;let m=e[n];if("function"==typeof m)if("function"==typeof t[n])m=o(e,e[n],t[n]);else if(c(r,n)){let t=i(n,r[n]);m=o(e,e[n],t)}else m=m.bind(e);else if("object"==typeof m&&null!==m&&(c(t,n)||c(r,n)))m=l(m,t[n],r[n]);else{if(!c(r,"*"))return Object.defineProperty(a,n,{configurable:!0,enumerable:!0,get:()=>e[n],set(t){e[n]=t}}),m;m=l(m,t[n],r["*"])}return a[n]=m,m},set:(t,r,s,n)=>(r in a?a[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(a,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(a,t)})},g=e=>({addListener(t,r,...a){t.addListener(e.get(r),...a)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),m=new r(e=>"function"!=typeof e?e:function(t){e(l(t,{},{getContent:{minArgs:0,maxArgs:0}}))}),d=new r(e=>"function"!=typeof e?e:function(t,r,s){let n,i,o=!1,c=new Promise(e=>{n=function(t){o=!0,e(t)}});try{i=e(t,r,n)}catch(e){i=Promise.reject(e)}let l=!0!==i&&a(i);if(!0!==i&&!l&&!o)return!1;return(l?i:c).then(e=>{s(e)},e=>{let t;s({__mozWebExtensionPolyfillReject__:!0,message:e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred"})}).catch(e=>{console.error("Failed to send onMessage rejected reply",e)}),!0}),u=({reject:t,resolve:r},a)=>{e.runtime.lastError?"The message port closed before a response was received."===e.runtime.lastError.message?r():t(Error(e.runtime.lastError.message)):a&&a.__mozWebExtensionPolyfillReject__?t(Error(a.message)):r(a)},p=(e,t,r,...a)=>{if(a.length<t.minArgs)throw Error(`Expected at least ${t.minArgs} ${n(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw Error(`Expected at most ${t.maxArgs} ${n(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise((e,t)=>{let s=u.bind(null,{resolve:e,reject:t});a.push(s),r.sendMessage(...a)})},A={devtools:{network:{onRequestFinished:g(m)}},runtime:{onMessage:g(d),onMessageExternal:g(d),sendMessage:p.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:p.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},h={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return t.privacy={network:{"*":h},services:{"*":h},websites:{"*":h}},l(e,A,t)})(chrome)},"function"==typeof define&&define.amd?define("webextension-polyfill",["module"],a):a(e)});var i=n("3OecN");let o=new TextEncoder,c=new TextDecoder;function l(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}function g(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:c.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=c.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),a=new Uint8Array(e.length);for(let t=0;t<e.length;t++)a[t]=e.charCodeAt(t);return a}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function m(e){let t=e;return("string"==typeof t&&(t=o.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class d extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class u extends d{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class p extends d{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class A extends d{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class h extends d{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class y extends d{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class f extends d{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class w extends d{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class b extends d{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}var S=(e,t)=>{let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:parseInt(e.slice(-3),10)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"Ed25519":case"EdDSA":return{name:"Ed25519"};default:throw new h(`alg ${e} is not supported either by JOSE or your javascript runtime`)}},x=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}};function E(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function v(e,t){return e.name===t}function k(e){return parseInt(e.name.slice(4),10)}function T(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}var C=(e,...t)=>T("Key must be ",e,...t);function P(e,t,...r){return T(`Key for the ${e} algorithm must be `,t,...r)}var H=async(e,t,r)=>{if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(C(t,"CryptoKey","KeyObject","JSON Web Key"));return crypto.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}return!function(e,t,r){switch(t){case"HS256":case"HS384":case"HS512":{if(!v(e.algorithm,"HMAC"))throw E("HMAC");let r=parseInt(t.slice(2),10);if(k(e.algorithm.hash)!==r)throw E(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!v(e.algorithm,"RSASSA-PKCS1-v1_5"))throw E("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(k(e.algorithm.hash)!==r)throw E(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!v(e.algorithm,"RSA-PSS"))throw E("RSA-PSS");let r=parseInt(t.slice(2),10);if(k(e.algorithm.hash)!==r)throw E(`SHA-${r}`,"algorithm.hash");break}case"Ed25519":case"EdDSA":if(!v(e.algorithm,"Ed25519"))throw E("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!v(e.algorithm,"ECDSA"))throw E("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw E(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}if(r&&!e.usages.includes(r))throw TypeError(`CryptoKey does not support this operation, its usages must include ${r}.`)}(t,e,r),t},R=async(e,t,r,a)=>{let s=await H(e,t,"verify");x(e,s);let n=S(e,s.algorithm);try{return await crypto.subtle.verify(n,s,r,a)}catch{return!1}},_=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},I=e=>{if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function W(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function K(e){return e?.[Symbol.toStringTag]==="KeyObject"}var O=e=>W(e)||K(e);function $(e){return I(e)&&"string"==typeof e.kty}let j=e=>e?.[Symbol.toStringTag],D=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let a;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):a=r;break;case e.startsWith("PBES2"):a="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):a=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):a="wrapKey";break;case"decrypt"===r:a=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(a&&t.key_ops?.includes?.(a)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${a}" when present`)}return!0},J=(e,t,r)=>{if(!(t instanceof Uint8Array)){if($(t)){if("oct"===t.kty&&"string"==typeof t.k&&D(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!O(t))throw TypeError(P(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${j(t)} instances for symmetric algorithms must be of type "secret"`)}},N=(e,t,r)=>{if($(t))switch(r){case"decrypt":case"sign":if("oct"!==t.kty&&"string"==typeof t.d&&D(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if("oct"!==t.kty&&void 0===t.d&&D(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!O(t))throw TypeError(P(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${j(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${j(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${j(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${j(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${j(t)} instances for asymmetric algorithm encryption must be of type "public"`)}};var M=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?J(e,t,r):N(e,t,r)},U=(e,t,r,a,s)=>{let n;if(void 0!==s.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let i of(n=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!n.has(i))throw new h(`Extension Header Parameter "${i}" is not recognized`);if(void 0===s[i])throw new e(`Extension Header Parameter "${i}" is missing`);if(n.get(i)&&void 0===a[i])throw new e(`Extension Header Parameter "${i}" MUST be integrity protected`)}return new Set(a.crit)},L=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)},B=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new h('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new h('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new h('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new h('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),a={...e};return delete a.alg,delete a.use,crypto.subtle.importKey("jwk",a,t,e.ext??!e.d,e.key_ops??r)};let F=async(t,r,a,s=!1)=>{let n=(e||=new WeakMap).get(t);if(n?.[a])return n[a];let i=await B({...r,alg:a});return s&&Object.freeze(t),n?n[a]=i:e.set(t,{[a]:i}),i},q=(t,r)=>{let a,s=(e||=new WeakMap).get(t);if(s?.[r])return s[r];let n="public"===t.type,i=!!n;if("x25519"===t.asymmetricKeyType){switch(r){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}a=t.toCryptoKey(t.asymmetricKeyType,i,n?[]:["deriveBits"])}if("ed25519"===t.asymmetricKeyType){if("EdDSA"!==r&&"Ed25519"!==r)throw TypeError("given KeyObject instance cannot be used for this algorithm");a=t.toCryptoKey(t.asymmetricKeyType,i,[n?"verify":"sign"])}if("rsa"===t.asymmetricKeyType){let e;switch(r){case"RSA-OAEP":e="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":e="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":e="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":e="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(r.startsWith("RSA-OAEP"))return t.toCryptoKey({name:"RSA-OAEP",hash:e},i,n?["encrypt"]:["decrypt"]);a=t.toCryptoKey({name:r.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:e},i,[n?"verify":"sign"])}if("ec"===t.asymmetricKeyType){let e=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(t.asymmetricKeyDetails?.namedCurve);if(!e)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===r&&"P-256"===e&&(a=t.toCryptoKey({name:"ECDSA",namedCurve:e},i,[n?"verify":"sign"])),"ES384"===r&&"P-384"===e&&(a=t.toCryptoKey({name:"ECDSA",namedCurve:e},i,[n?"verify":"sign"])),"ES512"===r&&"P-521"===e&&(a=t.toCryptoKey({name:"ECDSA",namedCurve:e},i,[n?"verify":"sign"])),r.startsWith("ECDH-ES")&&(a=t.toCryptoKey({name:"ECDH",namedCurve:e},i,n?[]:["deriveBits"]))}if(!a)throw TypeError("given KeyObject instance cannot be used for this algorithm");return s?s[r]=a:e.set(t,{[r]:a}),a};var G=async(e,t)=>{if(e instanceof Uint8Array||W(e))return e;if(K(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return q(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return F(e,r,t)}if($(e))return e.k?g(e.k):F(e,e,t,!0);throw Error("unreachable")};async function V(e,t,r){let a,s;if(!I(e))throw new y("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new y('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new y("JWS Protected Header incorrect type");if(void 0===e.payload)throw new y("JWS Payload missing");if("string"!=typeof e.signature)throw new y("JWS Signature missing or incorrect type");if(void 0!==e.header&&!I(e.header))throw new y("JWS Unprotected Header incorrect type");let n={};if(e.protected)try{let t=g(e.protected);n=JSON.parse(c.decode(t))}catch{throw new y("JWS Protected Header is invalid")}if(!_(n,e.header))throw new y("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let i={...n,...e.header},m=U(y,new Map([["b64",!0]]),r?.crit,n,i),d=!0;if(m.has("b64")&&"boolean"!=typeof(d=n.b64))throw new y('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:u}=i;if("string"!=typeof u||!u)throw new y('JWS "alg" (Algorithm) Header Parameter missing or invalid');let p=r&&L("algorithms",r.algorithms);if(p&&!p.has(u))throw new A('"alg" (Algorithm) Header Parameter value not allowed');if(d){if("string"!=typeof e.payload)throw new y("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new y("JWS Payload must be a string or an Uint8Array instance");let h=!1;"function"==typeof t&&(t=await t(n,e),h=!0),M(u,t,"verify");let f=l(o.encode(e.protected??""),o.encode("."),"string"==typeof e.payload?o.encode(e.payload):e.payload);try{a=g(e.signature)}catch{throw new y("Failed to base64url decode the signature")}let w=await G(t,u);if(!await R(u,w,a,f))throw new b;if(d)try{s=g(e.payload)}catch{throw new y("Failed to base64url decode the payload")}else s="string"==typeof e.payload?o.encode(e.payload):e.payload;let S={payload:s};return(void 0!==e.protected&&(S.protectedHeader=n),void 0!==e.header&&(S.unprotectedHeader=e.header),h)?{...S,key:w}:S}async function z(e,t,r){if(e instanceof Uint8Array&&(e=c.decode(e)),"string"!=typeof e)throw new y("Compact JWS must be a string or Uint8Array");let{0:a,1:s,2:n,length:i}=e.split(".");if(3!==i)throw new y("Invalid Compact JWS");let o=await V({payload:s,protected:a,signature:n},t,r),l={payload:o.payload,protectedHeader:o.protectedHeader};return"function"==typeof t?{...l,key:o.key}:l}var Z=e=>Math.floor(e.getTime()/1e3);let X=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;var Y=e=>{let t,r=X.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t};function Q(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let ee=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,et=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class er{#e;constructor(e){if(!I(e))throw TypeError("JWT Claims Set MUST be an object");this.#e=structuredClone(e)}data(){return o.encode(JSON.stringify(this.#e))}get iss(){return this.#e.iss}set iss(e){this.#e.iss=e}get sub(){return this.#e.sub}set sub(e){this.#e.sub=e}get aud(){return this.#e.aud}set aud(e){this.#e.aud=e}set jti(e){this.#e.jti=e}set nbf(e){"number"==typeof e?this.#e.nbf=Q("setNotBefore",e):e instanceof Date?this.#e.nbf=Q("setNotBefore",Z(e)):this.#e.nbf=Z(new Date)+Y(e)}set exp(e){"number"==typeof e?this.#e.exp=Q("setExpirationTime",e):e instanceof Date?this.#e.exp=Q("setExpirationTime",Z(e)):this.#e.exp=Z(new Date)+Y(e)}set iat(e){void 0===e?this.#e.iat=Z(new Date):e instanceof Date?this.#e.iat=Q("setIssuedAt",Z(e)):"string"==typeof e?this.#e.iat=Q("setIssuedAt",Z(new Date)+Y(e)):this.#e.iat=Q("setIssuedAt",e)}}async function ea(e,t,r){let a=await z(e,t,r);if(a.protectedHeader.crit?.includes("b64")&&!1===a.protectedHeader.b64)throw new f("JWTs MUST NOT use unencoded payload");let s={payload:function(e,t,r={}){let a,s;try{a=JSON.parse(c.decode(t))}catch{}if(!I(a))throw new f("JWT Claims Set must be a top-level JSON object");let{typ:n}=r;if(n&&("string"!=typeof e.typ||ee(e.typ)!==ee(n)))throw new u('unexpected "typ" JWT header value',a,"typ","check_failed");let{requiredClaims:i=[],issuer:o,subject:l,audience:g,maxTokenAge:m}=r,d=[...i];for(let e of(void 0!==m&&d.push("iat"),void 0!==g&&d.push("aud"),void 0!==l&&d.push("sub"),void 0!==o&&d.push("iss"),new Set(d.reverse())))if(!(e in a))throw new u(`missing required "${e}" claim`,a,e,"missing");if(o&&!(Array.isArray(o)?o:[o]).includes(a.iss))throw new u('unexpected "iss" claim value',a,"iss","check_failed");if(l&&a.sub!==l)throw new u('unexpected "sub" claim value',a,"sub","check_failed");if(g&&!et(a.aud,"string"==typeof g?[g]:g))throw new u('unexpected "aud" claim value',a,"aud","check_failed");switch(typeof r.clockTolerance){case"string":s=Y(r.clockTolerance);break;case"number":s=r.clockTolerance;break;case"undefined":s=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:A}=r,h=Z(A||new Date);if((void 0!==a.iat||m)&&"number"!=typeof a.iat)throw new u('"iat" claim must be a number',a,"iat","invalid");if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw new u('"nbf" claim must be a number',a,"nbf","invalid");if(a.nbf>h+s)throw new u('"nbf" claim timestamp check failed',a,"nbf","check_failed")}if(void 0!==a.exp){if("number"!=typeof a.exp)throw new u('"exp" claim must be a number',a,"exp","invalid");if(a.exp<=h-s)throw new p('"exp" claim timestamp check failed',a,"exp","check_failed")}if(m){let e=h-a.iat;if(e-s>("number"==typeof m?m:Y(m)))throw new p('"iat" claim timestamp check failed (too far in the past)',a,"iat","check_failed");if(e<0-s)throw new u('"iat" claim timestamp check failed (it should be in the past)',a,"iat","check_failed")}return a}(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...s,key:a.key}:s}var es=async(e,t,r)=>{let a=await H(e,t,"sign");return x(e,a),new Uint8Array(await crypto.subtle.sign(S(e,a.algorithm),a,r))};class en{#e;#t;#r;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this.#e=e}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setUnprotectedHeader(e){if(this.#r)throw TypeError("setUnprotectedHeader can only be called once");return this.#r=e,this}async sign(e,t){let r;if(!this.#t&&!this.#r)throw new y("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!_(this.#t,this.#r))throw new y("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let a={...this.#t,...this.#r},s=U(y,new Map([["b64",!0]]),t?.crit,this.#t,a),n=!0;if(s.has("b64")&&"boolean"!=typeof(n=this.#t.b64))throw new y('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:i}=a;if("string"!=typeof i||!i)throw new y('JWS "alg" (Algorithm) Header Parameter missing or invalid');M(i,e,"sign");let g=this.#e;n&&(g=o.encode(m(g)));let d=l(r=this.#t?o.encode(m(JSON.stringify(this.#t))):o.encode(""),o.encode("."),g),u=await G(e,i),p={signature:m(await es(i,u,d)),payload:""};return n&&(p.payload=c.decode(g)),this.#r&&(p.header=this.#r),this.#t&&(p.protected=c.decode(r)),p}}class ei{#a;constructor(e){this.#a=new en(e)}setProtectedHeader(e){return this.#a.setProtectedHeader(e),this}async sign(e,t){let r=await this.#a.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}class eo{#t;#s;constructor(e={}){this.#s=new er(e)}setIssuer(e){return this.#s.iss=e,this}setSubject(e){return this.#s.sub=e,this}setAudience(e){return this.#s.aud=e,this}setJti(e){return this.#s.jti=e,this}setNotBefore(e){return this.#s.nbf=e,this}setExpirationTime(e){return this.#s.exp=e,this}setIssuedAt(e){return this.#s.iat=e,this}setProtectedHeader(e){return this.#t=e,this}async sign(e,t){let r=new ei(this.#s.data());if(r.setProtectedHeader(this.#t),Array.isArray(this.#t?.crit)&&this.#t.crit.includes("b64")&&!1===this.#t.b64)throw new f("JWTs MUST NOT use unencoded payload");return r.sign(e,t)}}function ec(e){let t="";for(;t.length<e;)t+=Math.random().toString(36).substring(2);return t.substring(0,e)}function el({prefix:e,storageKey:r}={}){return e=e||"",r=r||"ezrevenueDeviceId",async function(){let a=(await t(i).storage.local.get(r))[r];return a?console.log(`get deviceId ${a}`):(a=e+function(){let e=Date.now().toString(36),t=ec(22);return`${e}${t}`}(),console.log(`create deviceId ${a}`),await t(i).storage.local.set({[r]:a})),a}}function eg({projectId:e,projectSecret:r,paywallAlias:a,customerIdGetter:s}){a=a||"paywall_vip",s||(s=el());let n=s,o={customerInfo:null,lastChecked:null};async function c(e){return await l(e)}async function l(e){if(e?.refresh&&(o.customerInfo=null,o.lastChecked=null),o.customerInfo){let e=o.lastChecked,t=Date.now()-e>18e5;(!e||t)&&(o.customerInfo=null,o.lastChecked=null)}return o.customerInfo||(o.customerInfo=await g(),o.lastChecked=Date.now()),o.customerInfo}async function g(){let t=function({projectId:e,projectSecret:t}){let r={async decodeToken(e){let r=new TextEncoder().encode(t),{payload:a}=await ea(e,r);return a.result},async encodeToken(r){r.exp=Date.now()+1800,r.nonce=ec(16);let a=new TextEncoder().encode(t),s=new eo(r).setProtectedHeader({alg:"HS256",project_id:e});return await s.sign(a)},sendRequest:({url:e,content:t})=>fetch(e,{method:"POST",headers:{"Content-Type":"text/plain"},body:t}),async call(e,t){let a=await r.encodeToken({method:e,params:t});try{let t=await r.sendRequest({url:"https://revenue.ezboti.com/api/v1/server/"+e,content:a}),s=await t.text();return await r.decodeToken(s)}catch(t){if(t.response){let{status:r,data:a}=t.response;console.log(`${e} failed status=${r}, body ==>`,a)}throw t}}};return r}({projectId:e,projectSecret:r}),s=await n(),i=await t.call("customer.info",{paywall_alias:a,customer:{external_id:s},include_balance:!0});return console.log("customerInfo",i),i}async function m(e){let t=await c();return await d(t,e)}async function d(e,r){let a=e.home_link.url;if(!a)return null;let s=r?.screenWidth||800,n=r?.screenHeight||600,o=Math.min(s-32,800),l=Math.min(n-32,600),g=(await t(i).windows.create({url:a,type:"popup",width:o,height:l,left:Math.round((s-o)/2),top:Math.round((n-l)/2)})).id;return console.log(`Window created with ID: ${g}`),await new Promise(e=>{let r=async a=>{a===g&&(t(i).windows.onRemoved.removeListener(r),e(await c({refresh:!0})))};t(i).windows.onRemoved.addListener(r)})}async function u({equityId:e,equityAlias:t}={}){let r=(await c()).balance_s.find(r=>e?r.equity.id===e:r.equity.alias===t||"equity_vip");return r?.is_balance_usable}let p={getCustomerId:n,getCustomerInfo:c,showPaywallPopup:m,isBalanceUsable:u},A={};return Object.keys(p).forEach(e=>{A[`ezrevenue_${e}`]=p[e]}),t(i).runtime.onMessage.addListener(e=>{if(!e.action)return;let t=A[e.action];if(t)return console.log("ezrevenue request:",e),t(e.data)}),{projectId:e,projectSecret:r,paywallAlias:a,...p}}export{el as createDeviceIdGetter,eg as registerEzrevenueBackground};
//# sourceMappingURL=background.js.map
