{"Peizhi": {"description": "صفحة الإعدادات للنافذة المنبثقة", "message": "الإعدادات"}, "Zhuye": {"description": "الصفحة الرئيسية للنافذة المنبثقة", "message": "الرئيسية"}, "extDescription": {"description": "وصف الإضافة", "message": "العثور على أشياء مثيرة للاهتمام في كود المصدر أو JavaScript للصفحة"}, "extName": {"description": "اسم الإضافة", "message": "FindSomething"}, "getstartedContent": {"description": "getstarted", "message": "كيف يمكنك استخدامه جيدًا إذا لم تفهم الميزات؟\n## ميزات الصفحة الرئيسية\n* مطابقة وعرض الحقول مثل IP، IP+منفذ، النطاق، رقم الهوية، رقم الهاتف، البريد الإلكتروني، JWT، خوارزميات التشفير، المعلومات الحساسة، المسار، المسار غير الكامل، عنوان URL الكامل، المسار الثابت، إلخ.\n\n* تحليل الكود الديناميكي واستعادته بالذكاء الاصطناعي\n    * اكتشاف وتحليل الكود الذي يتم تحميله ديناميكيًا في JavaScript، بما في ذلك الكود الناتج عن أدوات تجميع webpack.\n    * بالنقر على < السوداء قبل الدوال المشتبه في أنها تولد ملفات JS ديناميكيًا، يمكنك استخدام واجهة OpenAI المدمجة لتحليل الكود الديناميكي واستعادته، والحصول في النهاية على جميع ملفات JS للمشروع بالكامل. هذا يعني أن الإضافة يمكنها جمع المعلومات عن المشروع بالكامل، متجاوزة منطق التفعيل في الواجهة الأمامية والتحقق من الأذونات. تم التحقق حاليًا من عملها مع الدوال في الشكل function(e){return a.p+\"\"+{4:\"679e89\"}[e]+\".js\"}.\n    * بمجرد النقر على < السوداء لتحليل الكود الديناميكي، سيعرض رمز الإضافة تقدم طلب الذكاء الاصطناعي، مثل 1/3.\n    * قد يكون تحليل الذكاء الاصطناعي بطيئًا، ويتطلب الانتظار حوالي 30 ثانية.\n    * سيتم الإشارة إلى عدد ملفات JS المستعادة من الذكاء الاصطناعي من خلال إشعارات toast.\n    * سيتم البحث مرة أخرى عن المعلومات في ملفات JS التي تم استعادتها بالذكاء الاصطناعي، وسيتم عرض المحتوى الجديد مع < السوداء.\n    * في صفحة الإعدادات، يمكنك تخصيص عنوان واجهة برمجة التطبيقات، المفتاح، النموذج، والتعليمات. النماذج التي تم التحقق منها تشمل doubao-1-5-lite-32k-250115 و deepseek-chat.\n    * هذه الميزة ستكون مدفوعة كعضوية. حالة العضوية مرتبطة بمعرف الجهاز ومخزنة في chrome.storage، لذا كن حذرًا من عدم مسح هذه المعلومات. يمكنك محاولة كسرها، الأمر ليس صعبًا، لكن يرجى عدم نشر طريقة الكسر.\n\n* النسخ\n    * زر نسخ لكل منطقة، يمكنه نسخ محتوى المنطقة المقابلة\n    * زر \"نسخ عنوان URL\" منفصل يمكنه نسخ محتوى منطقة PATH وإضافة عنوان الصفحة الحالي لكل سطر لتكوين عنوان URL كامل.\n\n* مصدر المعلومات\n    * كل معلومة معروضة يسبقها '<' برتقالي. مرر الماوس فوقها وانتظر لمدة ثانيتين لرؤية مصدر المعلومة. يمكنك أيضًا فتح الرابط المصدر عن طريق \"Ctrl+نقرة\" أو \"انقر بزر الماوس الأيمن - فتح الرابط في علامة تبويب جديدة\".\n\n* عرض التقدم\n    * يتم عرض تقدم المعالجة أسفل صفحة \"الرئيسية\"، مثل المعالجة: 1/20، حيث 1 هو عدد الروابط المطلوبة و20 هو إجمالي عدد الروابط المطلوب طلبها. إذا تم الانتهاء من جميع الطلبات، سيتم عرض \"اكتمل: 20/20\".\n    * منطق التحديث الكامل للصفحة يعتمد أيضًا على تقدم المعالجة، حيث يتم تحديث البيانات عند حدوث تغييرات قبل اكتمال المعالجة.\n\n## ميزات صفحة الإعدادات\n\n* مسح ذاكرة التخزين المؤقت\n    * نظرًا لأن البيانات المستخرجة يتم تخزينها في localstorage بالمتصفح، فقد تشغل الذاكرة إذا لم يتم مسحها لفترة طويلة.\n    * أضافت الإضافة منطق انتهاء صلاحية تلقائي في الإصدار 2.0.17، حيث تنتهي صلاحية البيانات التي لم يتم الوصول إليها خلال 7 أيام.\n    * يمكن للمستخدمين أيضًا النقر يدويًا على \"مسح\" لمسح البيانات المخزنة على الفور.\n\n* النافذة العائمة العالمية\n    * عند تفعيلها، سيتم تضمين صفحة FindSomething في كل صفحة مفتوحة، مما يلغي الحاجة للتفاعل مع الإضافة.\n    * ومع ذلك، لم تتم إضافة ميزات جديدة إلى النافذة العائمة العالمية بعد، ويتم الحفاظ فقط على الوظائف الأساسية.\n\n* مهلة تلقائية\n    * هذا الإعداد متوقف افتراضيًا. عند تفعيله، سيتم إنهاء الطلبات التي تبدأها الإضافة بعد ثانيتين لتجنب الانتظار الطويل.\n\n* الوضع الآمن\n    * في الوضع الآمن، ستصل الإضافة فقط إلى موارد JavaScript لتجنب مغادرة الصفحة الحالية أو الوصول إلى روابط العمليات الحساسة.\n    * عند إيقاف الوضع الآمن، ستصل الإضافة إلى جميع عناوين src و href في الصفحة الحالية، بغض النظر عن نوع الرابط، بما في ذلك CSS أو الصور أو واجهات برمجة التطبيقات، مما قد يؤدي إلى طلبات غير متوقعة.\n    * الوضع الآمن مفعل افتراضيًا.\n\n* تحليل الكود الديناميكي\n    * يتحكم في مفتاح ميزة تحليل الكود الديناميكي. عند إيقافه، لن يتحقق من حالة العضوية أو يقوم بتحليل الكود الديناميكي.\n\n* Webhook\n    * أوصي باستخدام POST، حيث أن GET له قيود على طول المعلمات.\n    * إذا تم كتابة اسم المعلمة في موقع معلمات الطلب، فسيكون جسم الطلب بتنسيق data={\\\"xx\\\":\\\"xx\\\"}.\n    * إذا لم يتم كتابة اسم المعلمة، فسيكون جسم الطلب بتنسيق JSON.\n    * يتضمن جسم الطلب التكوين، التقدم، جميع المعلومات المستخرجة، ومصادرها.\n    * بعض المواقع تستخرج الكثير من المحتوى، لذا انتبه إلى تكوين موقع webhook الخاص بك للتعامل مع هذا الكم الكبير من البيانات في طلب واحد.\n\n* قائمة النطاقات المسموح بها\n    * المنطق يعتمد على الانتهاء بـ xxx. النطاقات الموجودة في القائمة البيضاء لن يتم طلبها.\n\n* إعدادات الذكاء الاصطناعي\n    * يمكنك تخصيص عنوان واجهة برمجة التطبيقات (اكتب إلى دليل chat، مثل https://api.deepseek.com/chat/ أو https://ark.cn-beijing.volces.com/api/v3/chat)، المفتاح، النموذج (النماذج التي تم التحقق منها تشمل doubao-1-5-lite-32k-250115 و deepseek-chat)، التعليمات النظامية وتعليمات المستخدم. يمثل {url} في تعليمات المستخدم مسار JS الحالي، ويستخدمه الذكاء الاصطناعي لاستنتاج المسارات النسبية."}, "popupAIAnalysis": {"description": "تلميح التحليل بالذكاء الاصطناعي في الصفحة المنبثقة", "message": "انقر للتحليل بالذكاء الاصطناعي"}, "popupAlgorithm": {"description": "عنوان في الصفحة المنبثقة: الخوارزمية", "message": "الخوارزمية"}, "popupAnalyze": {"description": "عنوان في الصفحة المنبثقة: تحليل الكود الديناميكي", "message": "تحليل الكود الديناميكي"}, "popupClickToCollapse": {"description": "تلميح النقر للطي", "message": "انقر للطي"}, "popupClickToExpand": {"description": "تلميح النقر للتوسيع", "message": "انقر للتوسيع"}, "popupComplete": {"description": "تلميح التقدم في الصفحة المنبثقة: اكتملت المعالجة", "message": "اكتملت المعالجة:"}, "popupCopy": {"description": "زر النسخ في الصفحة المنبثقة", "message": "نسخ"}, "popupCopyurl": {"description": "زر نسخ الرابط في الصفحة المنبثقة", "message": "نسخ الرابط"}, "popupDomain": {"description": "عنوان في الصفحة المنبثقة: النطاق", "message": "النطاق"}, "popupIncompletePath": {"description": "عنوان في الصفحة المنبثقة: مسار غير كامل", "message": "مسار غير كامل"}, "popupIp": {"description": "عنوان في الصفحة المنبثقة: الآي بي", "message": "الآي بي"}, "popupIpPort": {"description": "عنوان في الصفحة المنبثقة: الآي بي والمنفذ", "message": "الآي بي والمنفذ"}, "popupJwt": {"description": "عنوان في الصفحة المنبثقة: JWT", "message": "JWT"}, "popupMail": {"description": "عنوان في الصفحة المنبثقة: الب<PERSON>ي<PERSON> الإلكتروني", "message": "الب<PERSON>يد الإلكتروني"}, "popupMobile": {"description": "عنوان في الصفحة المنبثقة: رقم الهاتف المحمول", "message": "رقم الهات<PERSON> المحمول"}, "popupPath": {"description": "عنوان في الصفحة المنبثقة: المسار", "message": "المسار"}, "popupProcessing": {"description": "تلميح التقدم في الصفحة المنبثقة: جاري المعالجة", "message": "جاري المعالجة..."}, "popupSecret": {"description": "عنوان في الصفحة المنبثقة: معلومات حساسة", "message": "معلومات حساسة"}, "popupSfz": {"description": "عنوان في الصفحة المنبثقة: بطاقة الهوية", "message": "بطاقة الهوية"}, "popupStaticPath": {"description": "عنوان في الصفحة المنبثقة: مسار ثابت", "message": "مسار ثابت"}, "popupTipClickBeforeCopy": {"description": "تلميح النسخ في الصفحة المنبثقة عند حدوث خطأ", "message": "الرجاء النقر على الصفحة الأصلية قبل النسخ :)"}, "popupUrl": {"description": "عنوان في الصفحة المنبثقة: الرابط", "message": "الرابط"}, "popupVipFeature": {"description": "تلميح ميزة العضوية في الصفحة المنبثقة", "message": "ميزة العضوية"}, "popupVipMember": {"description": "زر العضوية في الصفحة المنبثقة: عضوي", "message": "عضوي"}, "popupVipSubscribe": {"description": "زر العضوية في الصفحة المنبثقة: اشترك في العضوية", "message": "اشترك في العضوية"}, "popupVipSwitchOff": {"description": "نص الزر عندما يكون مفتاح VIP مغلقًا في الصفحة المنبثقة", "message": "يرجى تشغيل مفتاح VIP"}, "popupVipSwitchOffAlert": {"description": "تنبيه عند النقر على الزر مع إيقاف مفتاح VIP في الصفحة المنبثقة", "message": "يرجى تشغيل مفتاح VIP في صفحة الإعدادات أولاً"}, "settingAI": {"description": "عنوان في صفحة الإعدادات: إعدادات الذكاء الاصطناعي", "message": "إعدادات الذكاء الاصطناعي"}, "settingAISystemPrompt": {"description": "عنصر الإعداد في صفحة الإعدادات: تلميح النظام للذكاء الاصطناعي", "message": "تلميح النظام"}, "settingAIUserPrompt": {"description": "عنصر الإعداد في صفحة الإعدادات: تلميح المستخدم للذكاء الاصطناعي", "message": "تلميح المستخدم"}, "settingAIapiKey": {"description": "عنصر الإعداد في صفحة الإعدادات: مفتاح الـ API للذكاء الاصطناعي", "message": "مفتاح الـ API"}, "settingAIbaseURL": {"description": "عنصر الإعداد في صفحة الإعدادات: عنوان URL للـ API للذكاء الاصطناعي", "message": "عنوان URL للـ API"}, "settingAImodel": {"description": "عنصر الإعداد في صفحة الإعدادات: نموذج الذكاء الاصطناعي", "message": "النموذج"}, "settingAutoTimeout": {"description": "عنوان في صفحة الإعدادات: المهلة التلقائية", "message": "المهلة التلقائية"}, "settingClearCache": {"description": "عنوان في صفحة الإعدادات: مسح الذاكرة المؤقتة", "message": "مس<PERSON> الذاكرة المؤقتة"}, "settingClearComplete": {"description": "تلميح في صفحة الإعدادات: اكتمل المسح", "message": "اكتمل المسح"}, "settingClearLocalStorage": {"description": "زر في صفحة الإعدادات: مسح", "message": "م<PERSON><PERSON>"}, "settingClosed": {"description": "زر في صفحة الإعدادات: مغلق", "message": "مغلق"}, "settingDomainAllowList": {"description": "عنصر الإعداد في صفحة الإعدادات: قائمة السماح للنطاقات", "message": "قائمة السماح للنطاقات"}, "settingDomainAllowListTip": {"description": "تلميح في صفحة الإعدادات لقائمة السماح للنطاقات", "message": "أدخل الجزء النهائي من النطاق، مفصولاً بسطور جديدة، إذا لم يتم التكوين يستخدم الافتراضي .google.com، .amazon.com، portswigger.net"}, "settingDynamicFunctionAnalysis": {"description": "عند تفعيل مفتاح VIP، فقط الأعضاء VIP يمكنهم استخدام الميزة. عند إيقافه، لا أحد يمكنه استخدامها.", "message": "م<PERSON><PERSON><PERSON><PERSON> VIP"}, "settingGlobalFloatingWindow": {"description": "عنوان في صفحة الإعدادات: نافذة عائمة عالمية", "message": "نافذة عائمة عالمية"}, "settingOpened": {"description": "زر في صفحة الإعدادات: مفتوح", "message": "مفتوح"}, "settingResetAndSave": {"description": "زر في صفحة الإعدادات: إعادة تعيين وحفظ", "message": "إعادة تعيين وحفظ"}, "settingSafe": {"description": "عنوان في صفحة الإعدادات: الوضع الآمن", "message": "الوضع الآمن"}, "settingSave": {"description": "زر في صفحة الإعدادات: حفظ", "message": "<PERSON><PERSON><PERSON>"}, "settingWebhook": {"description": "عنوان في صفحة الإعدادات: Webhook", "message": "Webhook"}, "settingWebhookArg": {"description": "عنصر الإعداد في صفحة الإعدادات: معاملات الطلب", "message": "معاملات الطلب"}, "settingWebhookHeaders": {"description": "عنصر الإعداد في صفحة الإعدادات: رؤوس مخصصة", "message": "رؤوس مخصصة"}, "settingWebhookMethod": {"description": "عنصر الإعداد في صفحة الإعدادات: طريقة الطلب", "message": "طريقة الطلب"}, "settingWebhookUrl": {"description": "عنصر الإعداد في صفحة الإعدادات: عنوان URL للرد", "message": "عنوان URL للرد"}}