<!--  
@Date    : 2020-09-12 16:26:48
<AUTHOR> residuallaugh 
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">    
    <script src="ezrevenue-sdk/ui.js"></script>
</head>
<body style="width:780px; font-size: 14px;">
    <div style="width:780px; height: 30px; margin-left: 15px;">
        <a href="popup.html"><div id="Zhuye" style="width: 55px; height: 28px; float: left; text-align: center; line-height: 28px; font-size: 14px; background: #000000; color: #ffffff; border: 1px solid black;border-radius: 2px 0px 0px 2px;">主页</div></a><a href="settings.html"><div id="Peizhi" style="width: 55px; height: 28px; float: left; text-align: center; line-height: 28px; font-size: 14px; background: #ffffff; color: #000000; border: 1px solid black;border-left: none;border-radius: 0px 0px 0px 0px;">配置</div></a><a href="#" id="manual-link"><div id="Shouce" style="width: 70px; height: 28px; float: left; text-align: center; line-height: 28px; font-size: 14px; background: #ffffff; color: #000000; border: 1px solid black;border-left: none;border-radius: 0px 2px 2px 0px;">使用手册</div></a>
        <button id="vip-button" style="width: 90px; height: 28px; float: right; margin-right: 25px; text-align: center; line-height: 28px; font-size: 14px; background: #FFD700; color: #000; border: 1px solid #FFD700; border-radius: 2px;">开通会员</button>
    </div>
    <div style="width:780px; height: 800px; margin-left: 15px;">
        <div id="taskstatus" style="height: 34px; line-height: 34px;"></div>
        <div style="width: 256px; float: left; border-right: 1px solid #e8e8e8;">
            <div class="section-container" data-section="ip">
                <div class="findsomething_title collapsible-title" id="popupIp">IP —</div><button type="button" class="copy" name="ip">复制</button>
                <div class="section-content">
            <p id="ip" style="word-break:break-word;">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="ip_port">
                <div class="findsomething_title collapsible-title" id="popupIpPort">IP_PORT —</div><button class="copy" name="ip_port">复制</button>
                <div class="section-content">
            <p id="ip_port" style="word-break:break-word;">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="domain">
                <div class="findsomething_title collapsible-title" id="popupDomain">域名 —</div><button class="copy" name="domain">复制</button>
                <div class="section-content">
            <p id="domain" style="word-break:break-word;">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="sfz">
                <div class="findsomething_title collapsible-title" id="popupSfz">身份证 —</div><button class="copy" name="sfz">复制</button>
                <div class="section-content">
            <p id="sfz" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="mobile">
                <div class="findsomething_title collapsible-title" id="popupMobile">手机号 —</div><button class="copy" name="mobile">复制</button>
                <div class="section-content">
            <p id="mobile" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="mail">
                <div class="findsomething_title collapsible-title" id="popupMail">邮箱 —</div><button class="copy" name="mail">复制</button>
                <div class="section-content">
            <p id="mail" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="jwt">
                <div class="findsomething_title collapsible-title" id="popupJwt">JWT —</div><button class="copy" name="jwt">复制</button>
                <div class="section-content">
            <p id="jwt" style="word-break:break-word;">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="algorithm">
                <div class="findsomething_title collapsible-title" id="popupAlgorithm">算法 —</div><button class="copy" name="algorithm">复制</button>
                <div class="section-content">
            <p id="algorithm" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="secret">
                <div class="findsomething_title collapsible-title" id="popupSecret">Secret —</div><button class="copy" name="secret">复制</button>
                <div class="section-content">
            <p id="secret" style="">🈚️</p>
                </div>
            </div>
        </div>
        <div style="width: 480px; height: 800px; float: left; margin-left:16px;">
            <div class="section-container" data-section="analyze">
                <div class="findsomething_title collapsible-title" id="popupAnalyze">动态代码分析 —</div>
                <div class="section-content">
            <p id="showCode" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="path">
                <div class="findsomething_title collapsible-title" id="popupPath">Path —</div><button id="path_button" class="copy" name="path">复制</button><button id="popupCopyurl" class="copy" name="path">复制URL</button>
                <div class="section-content">
            <p id="path" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="incomplete_path">
                <div class="findsomething_title collapsible-title" id="popupIncompletePath">IncompletePath —</div><button class="copy" name="incomplete_path">复制</button>
                <div class="section-content">
            <p id="incomplete_path" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="url">
                <div class="findsomething_title collapsible-title" id="popupUrl">Url —</div><button class="copy" name="url">复制</button>
                <div class="section-content">
            <p id="url" style="">🈚️</p>
                </div>
            </div>
            <div class="section-container" data-section="static">
                <div class="findsomething_title collapsible-title" id="popupStaticPath">StaticUrl —</div><button class="copy" name="static">复制</button>
                <div class="section-content">
            <p id="static" style="">🈚️</p>
                </div>
            </div>
        </div>
</div>
</body>
<!-- <script src="jquery-3.6.0.min.js"></script> -->
<script src="popup.js"> </script>
<style type="text/css">
    .copy {
        border-style: none;
        background-color: #ffffff;
        float: right;
        margin-right: 16px;
        
    }
    .findsomething_title {
        font-size: 16px;
        font-weight: bold;
        border-left: 4px solid black;
        text-indent: 4px;
        height: 16px;
        line-height: 16px;
    }
    .collapsible-title {
        cursor: pointer;
        user-select: none;
        transition: background-color 0.2s ease;
    }
    .collapsible-title:hover {
        background-color: #f5f5f5;
    }
    .collapsible-title.collapsed::after {
        content: "";
    }
    .collapsible-title.expanded::after {
        content: "";
    }
    .section-content {
        overflow: hidden;
        transition: max-height 0.3s ease;
        max-height: none;
        width: 100%;
        padding: 0 5px;
        box-sizing: border-box;
    }
    .section-content.collapsed {
        max-height: 0;
    }
    .tips {
        display: inline-block;
        border-top: 0.2px solid;
        border-right: 0.2px solid;
        width: 10px;
        height: 10px;
        border-color: #EA6000;
        transform: rotate(-135deg);
    }
    .tips2 {
        display: inline-block;
        border-top: 0.2px solid;
        border-right: 0.2px solid;
        width: 10px;
        height: 10px;
        border-color: #000000;
        transform: rotate(-135deg);
    }
    span {
        display: inline-block;
        word-wrap: break-word;
        width: calc(100% - 20px);
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 2.8em;
        overflow: hidden;
        transition: max-height 0.3s ease;
        vertical-align: top;
        position: relative;
        box-sizing: border-box;
    }
    /* 动态代码分析区域的span特殊样式 */
    #showCode span {
        width: calc(100% - 24px);
    }
    span.collapsible {
        cursor: pointer;
    }
    span.expanded {
        max-height: none;
    }
    span.collapsible {
        padding-right: 80px !important;
    }
    span.collapsible.collapsed::after {
        content: " ▼ [点击展开]";
        color: #007acc;
        font-weight: bold;
        font-size: 11px;
        position: absolute;
        right: 0;
        top: 0;
        background: transparent;
        padding: 0 2px;
        pointer-events: none;
    }
    span.collapsible.expanded::after {
        content: " ▲ [点击收起]";
        color: #007acc;
        font-weight: bold;
        font-size: 11px;
        position: absolute;
        right: 0;
        top: 0;
        background: transparent;
        padding: 0 2px;
        pointer-events: none;
    }
    span:hover {
        background-color: #f0f0f0;
    }
    span:active {
        background-color: #e0e0e0;
    }
    p {
        width: 100%;
        margin: 0;
        padding: 5px 0;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    a{
        display: inline-block;
        text-decoration:none;
        color:#333;
        vertical-align: top;
    }
    button{
        cursor: pointer
    }
</style>
</html>
