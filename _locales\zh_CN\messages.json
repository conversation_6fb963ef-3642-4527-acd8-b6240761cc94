{"Peizhi": {"description": "popup页面的配置", "message": "配置"}, "Zhuye": {"description": "popup页面的主页", "message": "主页"}, "extDescription": {"description": "扩展描述", "message": "在网页的源代码或js中找到一些有趣的东西"}, "extName": {"description": "扩展名", "message": "FindSomething"}, "getstartedContent": {"description": "getstarted", "message": "不了解功能你怎么能用得好呢？\n## 主页功能\n* ip、ip+端口、域名、身份证、手机号、邮箱、JWT、加密算法、敏感信息、路径、不完整的路径、完整的url、静态路径等字段匹配与展示。\n\n* 动态代码分析及AI还原\n    * 检测和分析js中的动态加载代码，包括webpack打包工具产生的动态加载代码。\n    * 通过点击疑似动态生成js文件的函数前黑色<，可以使用集成的OpenAI API对该行动态代码进行分析和还原，最终获取整个项目的全部js文件。这意味着插件能对整个项目进行信息搜集，绕过前端的触发逻辑和权限校验。目前验证对 function(e){return a.p+{4:'679e89'}[e]+'.js'} 形式的函数有效。\n    * 一旦点击黑色<的动态分析功能，插件图标下将会展示请求AI的进度，如1/3。\n    * AI分析的速度可能较慢，需等待约30秒。\n    * AI还原到的js文件量级将会通过toast提示。\n    * 经过AI还原得到的js文件会再次搜集信息，新增的内容将会以黑色<展示。\n    * 在配置页面可以自定义AI API地址、密钥、模型和提示词，经过验证的模型有 doubao-1-5-lite-32k-250115 和 deepseek-chat。\n    * 此功能将以会员形式付费。会员状态与设备id绑定，存储在chrome.storage，注意不要清除该信息。你可以尝试破解，这并不难，但请不要传播破解方法。\n\n* 复制\n    * 每一个区域的复制按钮，可以复制对应区域的内容\n    * 单独的复制URL按钮，可以复制PATH（路径）区域的内容，并在每行前面拼接当前页面的url，补充成为完整的url。\n\n* 信息来源\n    * 每一个展示出来的信息前有一个橘黄色的<，把鼠标放上去静默2秒可看到当前信息来源自哪个链接。你也可以通过“ctrl+左键”或“右键-在新标签页中打开链接”来打开该信息的来源链接。\n\n* 处理进度展示\n    * 在“主页”下方展示处理进度，如处理中：1/20，这里的1是已请求的链接数，20是要请求的链接总数。如果全部请求完成将展示为处理完成：20/20。\n    * 整个页面的刷新逻辑，也是基于处理进度进行刷新的，在处理完成前通过监听数据变动刷新数据。\n\n## 配置页功能\n\n* 清理缓存\n   * 因为提取到的数据都是存储在浏览器的localstorage里的，如果长时间不清理，可能会占用机器内存。\n   * 插件在2.0.17版本新增了自动过期逻辑，7天未访问的数据将会过期。\n   * 用户也可以手动点击“清理”，可以立即清空存储的数据。\n\n* 全局悬浮窗\n   * 打开后FindSomething的页面就会嵌入到每个打开的页面里，省去点开插件的交互。\n   * 但是目前没有把新的功能加到全局悬浮窗里，仅保持基础功能可用。\n\n* 自动超时\n  * 默认关闭的配置，打开后在插件后台发起的请求将会在2s后被终止，避免长时间等待。\n\n* 安全模式\n  * 在安全模式下插件仅会访问js资源，以避免退出当前页面或请求到敏感的操作链接。\n  * 在关闭安全模式后，会访问当前页面src、href属性的全部url，不区分链接类型，包括css、图片或是api等，这可能会导致出现预期外的请求。\n  * 安全模式默认为打开状态。\n\n* 动态代码分析\n  * 控制动态代码分析功能的开关，关闭后不会判断会员状态和动态代码分析。\n\n* Webhook回调\n  * 我建议使用post，因为get的参数有长度限制。\n  * 如果请求参数位置写了参数名，那么请求体就是data={“xx”:”xx”}这种格式。\n  * 如果请求参数位置没有写参数名，那么请求体会是json格式。\n  * 请求体里有包括配置、进度、提取到的全部信息和对应的来源。\n  * 有些网站提取到的内容特别多，需要注意配置你的webhook站点能在一个请求中接受这么多数据。\n\n* 域名白名单\n  * 判定逻辑是以xxx结尾，在白名单的域名不会请求。\n\n* AI配置\n  * 可以自定义配置API地址（写到chat目录下，如 https://api.deepseek.com/chat/ 或 https://ark.cn-beijing.volces.com/api/v3/chat ）、密钥、模型（经过验证的模型有 doubao-1-5-lite-32k-250115 和 deepseek-chat ）、系统提示词和用户提示词，用户提示词中的{url}占位符表示当前js的路径，用于AI推理相对路径。"}, "popupAIAnalysis": {"description": "popup页面的AI识别提示", "message": "点击进行AI识别"}, "popupAlgorithm": {"description": "popup页面的标题：算法", "message": "算法"}, "popupAnalyze": {"description": "popup页面的标题：动态代码分析", "message": "动态代码分析"}, "popupClickToCollapse": {"description": "点击收起文本的提示", "message": "点击收起"}, "popupClickToExpand": {"description": "点击展开文本的提示", "message": "点击展开"}, "popupComplete": {"description": "popup页面的进度提示：处理完成", "message": "处理完成："}, "popupCopy": {"description": "popup页面的复制按钮", "message": "复制"}, "popupCopyurl": {"description": "popup页面的复制URL按钮", "message": "复制URL"}, "popupDomain": {"description": "popup页面的标题：域名", "message": "域名"}, "popupIncompletePath": {"description": "popup页面的标题：不完整的路径", "message": "IncompletePath"}, "popupIp": {"description": "popup页面的标题：IP", "message": "IP"}, "popupIpPort": {"description": "popup页面的标题：IP_PORT", "message": "IP加端口"}, "popupJwt": {"description": "popup页面的标题：JWT", "message": "JWT"}, "popupMail": {"description": "popup页面的标题：邮箱", "message": "邮箱"}, "popupMobile": {"description": "popup页面的标题：手机号", "message": "手机号"}, "popupPath": {"description": "popup页面的标题：路径", "message": "PATH"}, "popupProcessing": {"description": "popup页面的进度提示：处理中", "message": "处理中.."}, "popupSecret": {"description": "popup页面的标题：敏感信息", "message": "敏感信息"}, "popupSfz": {"description": "popup页面的标题：身份证", "message": "身份证"}, "popupStaticPath": {"description": "popup页面的标题：静态路径", "message": "StaticPath"}, "popupTipClickBeforeCopy": {"description": "popup页面的点击复制时异常的提示：请点击原页面后再复制：）", "message": "请点击原页面后再复制：）"}, "popupUrl": {"description": "popup页面的标题：url", "message": "URL"}, "popupVipFeature": {"description": "popup页面的会员功能提示", "message": "会员功能"}, "popupVipMember": {"description": "popup页面的会员按钮：我的会员", "message": "我的会员"}, "popupVipSubscribe": {"description": "popup页面的会员按钮：开通会员", "message": "开通会员"}, "popupVipSwitchOff": {"description": "popup页面的VIP开关关闭时按钮文本", "message": "请打开VIP开关"}, "popupVipSwitchOffAlert": {"description": "popup页面的VIP开关关闭时点击按钮的提示", "message": "请先在设置页面打开VIP开关"}, "settingAI": {"description": "配置页的标题：AI配置", "message": "AI配置"}, "settingAISystemPrompt": {"description": "配置页的配置项：AI系统提示词", "message": "系统提示词"}, "settingAIUserPrompt": {"description": "配置页的配置项：AI用户提示词", "message": "用户提示词"}, "settingAIapiKey": {"description": "配置页的配置项：AI APIKEY", "message": "APIKEY"}, "settingAIbaseURL": {"description": "配置页的配置项：AI调用地址", "message": "调用地址"}, "settingAImodel": {"description": "配置页的配置项：AI model", "message": "model"}, "settingAutoTimeout": {"description": "配置页的标题：自动超时", "message": "自动超时"}, "settingClearCache": {"description": "配置页的标题：清理缓存", "message": "清理缓存"}, "settingClearComplete": {"description": "配置页的提示：清理完成", "message": "清理完成"}, "settingClearLocalStorage": {"description": "配置页的按钮：清理", "message": "清理"}, "settingClosed": {"description": "配置页的按钮：已关闭", "message": "已关闭"}, "settingDomainAllowList": {"description": "配置页的配置项：域名白名单", "message": "域名白名单"}, "settingDomainAllowListTip": {"description": "配置页的域名白名单里的提示", "message": "输入域名的结尾部分，以换行分隔，若不配置默认使用.google.com,.amazon.com,portswigger.net"}, "settingDynamicFunctionAnalysis": {"description": "配置页的标题：VIP开关", "message": "VIP开关"}, "settingGlobalFloatingWindow": {"description": "配置页的标题：全局悬浮窗", "message": "全局悬浮窗"}, "settingOpened": {"description": "配置页的按钮：已打开", "message": "已打开"}, "settingResetAndSave": {"description": "配置页的按钮：置空并保存", "message": "置空并保存"}, "settingSafe": {"description": "配置页的标题：安全模式", "message": "安全模式"}, "settingSave": {"description": "配置页的按钮：保存", "message": "保存"}, "settingWebhook": {"description": "配置页的标题：Webhook", "message": "Webhook"}, "settingWebhookArg": {"description": "配置页的配置项：请求方法", "message": "请求参数"}, "settingWebhookHeaders": {"description": "配置页的配置项：自定义headers", "message": "自定义headers"}, "settingWebhookMethod": {"description": "配置页的配置项：请求方法", "message": "请求方法"}, "settingWebhookUrl": {"description": "配置页的配置项：回调地址", "message": "回调地址"}}